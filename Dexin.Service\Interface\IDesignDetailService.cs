﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DesignDetailView;

namespace Dexin.Service.Interface
{
    public interface IDesignDetailService
    {
        Task<ResponseModels<IEnumerable<DesignDetailDTO>>> GetAllAsync();
        Task<ResponseModels<IEnumerable<DesignDetailDTO>>> GetByDesignIdAsync(int designId);
        Task<ResponseModels<DesignDetailDTO>> GetByIdAsync(int id);
        Task<ResponseModels<CreateDesignDetailDTO>> CreateAsync(int designId, CreateDesignDetailDTO dto);
        Task<ResponseModels<bool>> CreateListAsync(int id, List<CreateDesignDetailDTO> dto);
        Task<ResponseModels<UpdateDesignDetailDTO>> UpdateAsync(int designId, UpdateDesignDetailDTO dto);
        Task<ResponseModels<bool>> DeleteAsync(int id); 
        Task<ResponseModels<bool>> UpdateListByDesignIdAsync(int designId, List<UpdateDesignDetailDTO> list);

    }
}
