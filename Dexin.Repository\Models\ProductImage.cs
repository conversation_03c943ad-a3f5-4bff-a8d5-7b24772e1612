﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class ProductImage
{
    public int ProductImageId { get; set; }

    public string? ImageUrl { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ProductId { get; set; }

    public virtual Product? Product { get; set; }
}
