﻿using Dexin.Service.Commons.PaymentModels;
using Dexin.Service.DTOs.DesignPaymentView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Net.payOS;
using Net.payOS.Types;

namespace Dexin.APIServices.Payment
{
    [Route("api/[controller]")]
    [ApiController]

    public class PayOSPaymentsController : ControllerBase
    {
        private readonly PayOS _payOS;
        private readonly IDesignPaymentService _designPaymentService;
        public PayOSPaymentsController(PayOS payOS, IDesignPaymentService designPaymentService)
        {
            _payOS = payOS;
            _designPaymentService = designPaymentService;   
        }
        [HttpPost("create")] // Route: /api/order/create
        [Authorize]
        public async Task<IActionResult> CreatePaymentLink([FromBody] CreatePaymentLinkRequest body, int designId) // Ensure [FromBody]
        {
            try
            {
                int orderCode = int.Parse(DateTimeOffset.Now.ToString("ffffff"));
                ItemData item = new ItemData(body.productName, 1, body.price);
                List<ItemData> items = new List<ItemData> { item };
                PaymentData paymentData = new PaymentData(orderCode, body.price, body.description, items, body.cancelUrl, body.returnUrl);

                CreatePaymentResult createPayment = await _payOS.createPaymentLink(paymentData);

                /* CreatePaymentResult
                "bin": "970422",
                "accountNumber"
                "amount"
                "description"
                "orderCode"
                "currency"
                "paymentLinkId"
                "status"
                "expiredAt"
                "checkoutUrl"
                "qrCode"
                 */
                var designPayment = createPayment.FromResultLinkPayOSToCreateDTO();
                designPayment.DesignId = designId;
                var rs = await  _designPaymentService.CreateAsync(designPayment);

                // Return the payment link information as JSON
                return Ok(new Response(0, "success", createPayment));
            }
            catch (System.Exception exception)
            {
                Console.WriteLine(exception);
                return StatusCode(500, new Response(-1, "fail", null));
            }
        }

        [HttpGet("{orderId}")]
        public async Task<IActionResult> GetOrder([FromRoute] int orderId)
        {
            try
            {
                PaymentLinkInformation paymentLinkInformation = await _payOS.getPaymentLinkInformation(orderId);
                return Ok(new Response(0, "Ok", paymentLinkInformation));
            }
            catch (System.Exception exception)
            {
                Console.WriteLine(exception);
                return Ok(new Response(-1, "fail", null)); // Or NotFound, BadRequest depending on exception
            }
        }

        [HttpPut("{orderId}/cancel")]
        public async Task<IActionResult> CancelOrder([FromRoute] int orderId) // Renamed from just Put("{orderId}")
        {
            try
            {
                PaymentLinkInformation paymentLinkInformation = await _payOS.cancelPaymentLink(orderId);
                return Ok(new Response(0, "Ok", paymentLinkInformation));
            }
            catch (System.Exception exception)
            {
                Console.WriteLine(exception);
                return Ok(new Response(-1, "fail", null));
            }
        }

        [HttpPost("confirm-webhook")] // Route: /api/order/confirm-webhook
        public async Task<IActionResult> ConfirmWebhook([FromBody] ConfirmWebhook body) // Ensure [FromBody]
        {
            try
            {
                await _payOS.confirmWebhook(body.webhook_url);
                return Ok(new Response(0, "Ok", null));
            }
            catch (System.Exception exception)
            {
                Console.WriteLine(exception);
                return Ok(new Response(-1, "fail", null));
            }
        }

        [HttpPost("payos_transfer_handler")]
        public IActionResult payOSTransferHandler([FromBody] WebhookType body)
        {
            try
            {

                WebhookData data = _payOS.verifyPaymentWebhookData(body);

                Console.WriteLine($"Webhook received for order: {data.orderCode}, status: {data.description}");

                if (data.description == "Ma giao dich thu nghiem" || data.description == "VQRIO123")
                {
                    return Ok(new Response(0, "Ok - Test transaction received", null));
                }
                return Ok(new Response(0, "Ok - Webhook received", null));
            }
            catch (Exception e)
            {
                Console.WriteLine($"Webhook processing error: {e.Message}");
                return BadRequest(new Response(-1, "Webhook processing failed", null));
            }
        }
    }

}
