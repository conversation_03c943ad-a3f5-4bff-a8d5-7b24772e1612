﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IDecorItemRepository : IGenericRepository<DecorItem>
    {
        Task<IEnumerable<DecorItem>> GetAllIncludeAsync();
        Task<DecorItem> GetByIdIncludeAsync(int id);    
        Task<bool> CheckIdExisted(int id);  
    }
}
