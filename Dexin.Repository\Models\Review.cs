﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class Review
{
    public int ReviewId { get; set; }

    public byte? Rating { get; set; }

    public string? Title { get; set; }

    public string? Comment { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ProductId { get; set; }

    public int? OrderDetailId { get; set; }

    public int? UserAccountId { get; set; }

    public virtual OrderDetail? OrderDetail { get; set; }

    public virtual Product? Product { get; set; }

    public virtual SystemUserAccount? UserAccount { get; set; }
}
