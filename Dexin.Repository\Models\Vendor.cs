﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class Vendor
{
    public int VendorId { get; set; }

    public string? CompanyName { get; set; }

    public string? LogoUrl { get; set; }

    public string? Description { get; set; }

    public string? ContactEmail { get; set; }

    public string? Address { get; set; }

    public string? TaxId { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? UserAccountId { get; set; }

    public virtual ICollection<Color> Colors { get; set; } = new List<Color>();

    public virtual ICollection<Product> Products { get; set; } = new List<Product>();

    public virtual SystemUserAccount? UserAccount { get; set; }
}
