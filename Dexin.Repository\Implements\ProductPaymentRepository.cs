using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Repository.Implements
{
    public class ProductPaymentRepository : GenericRepository<ProductPayment>, IProductPaymentRepository
    {
        public ProductPaymentRepository(DexinContext context) => _context = context;

        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.ProductPayments.AnyAsync(p => p.ProductPaymentId == id);
        }

        public async Task<IEnumerable<ProductPayment>> GetAllIncludeAsync()
        {
            return await _context.ProductPayments
                .Include(p => p.UserAccount)
                .Include(p => p.Orders)
                    .ThenInclude(o => o.OrderDetails)
                        .ThenInclude(od => od.Product)
                .ToListAsync();
        }

        public async Task<ProductPayment> GetByIdIncludeAsync(int id)
        {
            return await _context.ProductPayments
                .Include(p => p.UserAccount)
                .Include(p => p.Orders)
                    .ThenInclude(o => o.OrderDetails)
                        .ThenInclude(od => od.Product)
                .FirstOrDefaultAsync(p => p.ProductPaymentId == id) ?? new ProductPayment();
        }

        public async Task<int> GetPaymentCountByStatusAsync(string status)
        {
            return await _context.ProductPayments
                .Where(p => p.Status == status)
                .CountAsync();
        }

        public async Task<IEnumerable<ProductPayment>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.ProductPayments
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
                .Include(p => p.UserAccount)
                .Include(p => p.Orders)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProductPayment>> GetPaymentsByUserIdAsync(int userId)
        {
            return await _context.ProductPayments
                .Where(p => p.UserAccountId == userId)
                .Include(p => p.Orders)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProductPayment>> GetRecentPaymentsAsync(int limit)
        {
            return await _context.ProductPayments
                .Include(p => p.UserAccount)
                .Include(p => p.Orders)
                .OrderByDescending(p => p.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<decimal> GetRevenueByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.ProductPayments
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate && 
                           p.Amount.HasValue && p.Status == "PAID")
                .SumAsync(p => p.Amount.Value);
        }

        public async Task<int> GetTotalPaymentsCountAsync()
        {
            return await _context.ProductPayments.CountAsync();
        }

        public async Task<decimal> GetTotalRevenueAsync()
        {
            return await _context.ProductPayments
                .Where(p => p.Amount.HasValue && p.Status == "PAID")
                .SumAsync(p => p.Amount.Value);
        }

        public async Task<int> GetPaymentCountByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.ProductPayments
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt < endDate)
                .CountAsync();
        }
    }
}
