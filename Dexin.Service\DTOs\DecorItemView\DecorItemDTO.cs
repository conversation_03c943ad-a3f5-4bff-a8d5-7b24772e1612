﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.ItemCategoryView;

namespace Dexin.Service.DTOs.DecorItemView
{
    public class DecorItemDTO
    {
        public int DecorItemId { get; set; }
        public string? Name { get; set; }

        public string? Description { get; set; }

        public string? ImageUrl { get; set; }

        public double? OriginWidth { get; set; }

        public double? OriginHeight { get; set; }

        public int? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public int? ItemCategoryId { get; set; }
        public List<DesignDetail> DesignDetails { get; set; } = new List<DesignDetail>();
        public ItemCategoryDTO ItemCategory { get; set; } = new ItemCategoryDTO();

    }
}
