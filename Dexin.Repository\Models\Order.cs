﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class Order
{
    public int OrdersId { get; set; }

    public string? OrderCode { get; set; }

    public int? Status { get; set; }

    public decimal? Subtotal { get; set; }

    public decimal? ShippingAmount { get; set; }

    public decimal? TotalAmount { get; set; }

    public string? PaymentMethod { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? UserAccountId { get; set; }

    public int? OrderAddressId { get; set; }

    public virtual OrderAddress? OrderAddress { get; set; }

    public virtual ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();

    public virtual ICollection<ProductPayment> ProductPayments { get; set; } = new List<ProductPayment>();

    public virtual SystemUserAccount? UserAccount { get; set; }
}
