﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class StaffFeedback
{
    public int StaffFeedbackId { get; set; }

    public byte? Rating { get; set; }

    public string? Comment { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? UserAccountId { get; set; }

    public int? StaffId { get; set; }

    public virtual SystemUserAccount? Staff { get; set; }

    public virtual SystemUserAccount? UserAccount { get; set; }
}
