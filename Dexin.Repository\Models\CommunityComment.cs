﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class CommunityComment
{
    public int CommunityCommentId { get; set; }

    public int? Status { get; set; }

    public string? Content { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? UserAccountId { get; set; }

    public int? CommunityPostId { get; set; }

    public virtual CommunityPost? CommunityPost { get; set; }

    public virtual SystemUserAccount? UserAccount { get; set; }
}
