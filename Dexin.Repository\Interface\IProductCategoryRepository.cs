﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IProductCategoryRepository : IGenericRepository<ProductCategory>
    {
        Task<IEnumerable<ProductCategory>> GetAllIncludeAsync();
        Task<ProductCategory> GetByIdInclude(int id);
        Task<bool> CheckIdExisted(int id);  
        Task<bool> CheckNameExisted(string name);   
    }
}
