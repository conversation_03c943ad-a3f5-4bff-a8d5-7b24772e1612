﻿using System;
using System.Collections.Generic;
using Dexin.Repository.Enums;

namespace Dexin.Repository.Models;

public partial class SystemUserAccount
{
    public int UserAccountId { get; set; }

    public string UserName { get; set; } = null!;

    public string Password { get; set; } = null!;

    public string FirstName { get; set; } = null!;

    public string LastName { get; set; } = null!;

    public string Email { get; set; } = null!;

    public string Phone { get; set; } = null!;

    public bool? Gender { get; set; }

    public string? EmployeeCode { get; set; } = null!;

    public Role RoleId { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? ModifiedBy { get; set; }

    public bool IsActive { get; set; }

    public string? Avartar { get; set; }

    public virtual ICollection<BlogLike> BlogLikes { get; set; } = new List<BlogLike>();

    public virtual ICollection<Blog> Blogs { get; set; } = new List<Blog>();

    public virtual ICollection<CommunityComment> CommunityComments { get; set; } = new List<CommunityComment>();

    public virtual ICollection<CommunityLike> CommunityLikes { get; set; } = new List<CommunityLike>();

    public virtual ICollection<CommunityPost> CommunityPosts { get; set; } = new List<CommunityPost>();

    public virtual ICollection<Design> DesignCreatedByNavigations { get; set; } = new List<Design>();

    public virtual ICollection<DesignPayment> DesignPayments { get; set; } = new List<DesignPayment>();

    public virtual ICollection<Design> DesignStaffs { get; set; } = new List<Design>();

    public virtual ICollection<FavoriteProduct> FavoriteProducts { get; set; } = new List<FavoriteProduct>();

    public virtual ICollection<OrderAddress> OrderAddresses { get; set; } = new List<OrderAddress>();

    public virtual ICollection<Order> Orders { get; set; } = new List<Order>();

    public virtual ICollection<ProductPayment> ProductPayments { get; set; } = new List<ProductPayment>();

    public virtual ICollection<Review> Reviews { get; set; } = new List<Review>();

    public virtual ICollection<Room> RoomStaffs { get; set; } = new List<Room>();

    public virtual ICollection<Room> RoomUsers { get; set; } = new List<Room>();

    public virtual ICollection<StaffFeedback> StaffFeedbackStaffs { get; set; } = new List<StaffFeedback>();

    public virtual ICollection<StaffFeedback> StaffFeedbackUserAccounts { get; set; } = new List<StaffFeedback>();

    public virtual ICollection<Vendor> Vendors { get; set; } = new List<Vendor>();
}
