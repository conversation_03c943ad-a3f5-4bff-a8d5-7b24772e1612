# Build Stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /source

# Copy everything into the container
COPY . .

# Restore dependencies
RUN dotnet restore "./Dexin.APIServices/Dexin.APIServices.csproj" --disable-parallel

# Publish the application in release mode to the /app directory
RUN dotnet publish "./Dexin.APIServices/Dexin.APIServices.csproj" -c Release -o /app --no-restore

# Serve Stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app

# Copy the published output from the build stage to the serve stage
COPY --from=build /app ./

# Set the ASP.NET Core URLs to listen on port 5000
ENV ASPNETCORE_URLS=http://+:5000

# Expose the application port
EXPOSE 5000

# Define the entry point for the container to run your application
ENTRYPOINT ["dotnet", "Dexin.APIServices.dll"]
