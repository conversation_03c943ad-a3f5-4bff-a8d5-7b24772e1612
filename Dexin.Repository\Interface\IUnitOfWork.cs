﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Storage;

namespace Dexin.Repository.Interface
{
    public interface IUnitOfWork
    {
        public ISystemUserAccountRepository SystemUserAccountRepository { get; }
        public IItemCategoryRepository ItemCategoryRepository { get; }
        public IDecorItemRepository DecorItemRepository { get; }
        public IBlogTagRepository BlogTagRepository { get; }
        public IDesignRepository DesignRepository { get; }
        public IBlogRepository BlogRepository { get; }
        public IDesignDetailRepository DesignDetailRepository { get; }
        public IDesignPaymentRepository DesignPaymentRepository { get; }
        public IRoomRepository RoomRepository { get; }
        public IVendorRepository VendorRepository { get; }
        public IProductCategoryRepository ProductCategoryRepository { get; }
        public Task<int> SaveChangeAsync();
        public Task<IDbContextTransaction> BeginTransactionAsync();
        public Task<int> CommitAsync();
        public Task RollbackAsync();
    }
}
