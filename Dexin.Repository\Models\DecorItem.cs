﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class DecorItem
{
    public int DecorItemId { get; set; }

    public string? Name { get; set; }

    public string? Description { get; set; }

    public string? ImageUrl { get; set; }

    public double? OriginWidth { get; set; }

    public double? OriginHeight { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ItemCategoryId { get; set; }

    public virtual ICollection<DesignDetail> DesignDetails { get; set; } = new List<DesignDetail>();

    public virtual ItemCategory? ItemCategory { get; set; }
}
