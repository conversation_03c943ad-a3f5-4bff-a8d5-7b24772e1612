﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Dexin.Service.Commons;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Dexin.Service.Implements
{
    public class ImageService : IImageService
    {
        private readonly Cloudinary _cloudinary;

        public ImageService(IOptions<CloudinarySetting> config)
        {
            var acc = new Account(
                config.Value.CloudName,
                config.Value.ApiKey,
                config.Value.ApiSecret);

            _cloudinary = new Cloudinary(acc);
        }

        public async Task<string> UploadImageAsync(IFormFile file)
        {
            if (file.Length > 0)
            {
                using (var stream = file.OpenReadStream())
                {
                    var uploadParams = new ImageUploadParams()
                    {
                        File = new FileDescription(file.FileName, stream),
                        Folder = "PTSM"
                    };

                    ImageUploadResult uploadResult = await _cloudinary.UploadAsync(uploadParams);
                    if (uploadResult.Error != null)
                    {
                        // Optional: log or throw detailed error
                        throw new Exception($"Cloudinary upload error: {uploadResult.Error.Message}");
                    }

                    return uploadResult.Url.ToString(); 
                }
            }

            return null;
        }

        public async Task<List<string>> UploadImagesAsync(List<IFormFile> files)
        {
            var urls = new List<string>();

            foreach (var file in files)
            {
                if (file.Length > 0)
                {
                    using (var stream = file.OpenReadStream())
                    {
                        var uploadParams = new ImageUploadParams
                        {
                            File = new FileDescription(file.FileName, stream),
                            Folder = "PTSM"
                        };

                        var result = await _cloudinary.UploadAsync(uploadParams);
                        if (result != null && result.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            urls.Add(result.Url.ToString());
                        }
                    }
                }
            }

            return urls;
        }


        public async Task<string> UploadObjFileAsync(IFormFile file)
        {
            if (file.Length > 0)
            {
                using (var stream = file.OpenReadStream())
                {
                    var uploadParams = new RawUploadParams()
                    {
                        File = new FileDescription(file.FileName, stream),
                        Folder = "3D"
                    };

                    RawUploadResult uploadResult = await _cloudinary.UploadAsync(uploadParams);

                    if (uploadResult.Error != null)
                    {
                        throw new Exception($"Cloudinary upload error: {uploadResult.Error.Message}");
                    }

                    return uploadResult.SecureUrl.ToString();
                }
            }

            return null;
        }

        public async Task<string> UploadLargeFileAsync(IFormFile file)
        {
            if (file.Length <= 0) return null;

            using var stream = file.OpenReadStream();
            var uploadParams = new RawUploadParams
            {
                File = new FileDescription(file.FileName, stream),
                //ResourceType = ResourceType.Raw, // important for .obj or any non-image file
                PublicId = Path.GetFileNameWithoutExtension(file.FileName), // Optional
                Overwrite = true
            };

            var result = await _cloudinary.UploadAsync(uploadParams);

            if (result.StatusCode == System.Net.HttpStatusCode.OK)
                return result.SecureUrl.ToString();

            throw new Exception("Upload to Cloudinary failed");
        }
    }
}
