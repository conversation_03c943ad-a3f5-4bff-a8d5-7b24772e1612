﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IBlogRepository : IGenericRepository<Blog>
    {
        Task<IEnumerable<Blog>> GetAllInCLudeAsync();
        Task<IEnumerable<Blog>> SearchIncludeAysnc(string title, string subtitle, string content);
        Task<bool> CheckIdExisted(int id);  

        Task<Blog> GetByIdIncludeAsync(int id); 

    }
}
