﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Dexin.Repository
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly DexinContext _context;
        private readonly ISystemUserAccountRepository _systemUserAccountRepository;
        private readonly IItemCategoryRepository _itemCategoryRepository;
        private readonly IDecorItemRepository _decorItemRepository;
        private readonly IBlogTagRepository _blogTagRepository;
        private readonly IBlogRepository _blogRepository;
        private readonly IDesignRepository _designRepository;
        private readonly IDesignDetailRepository _designDetailRepository;
        private readonly IDesignPaymentRepository _designPaymentRepository;
        private readonly IRoomRepository _roomRepository;
        private readonly IVendorRepository _vendorRepository;
        private readonly IProductCategoryRepository _productCategoryRepository;

        private IDbContextTransaction _transaction;
        public UnitOfWork(DexinContext context,
                        ISystemUserAccountRepository systemUserAccountRepository,
                        IItemCategoryRepository itemCategoryRepository,
                        IDecorItemRepository decorItemRepository,
                        IBlogTagRepository blogTagRepository,
                        IBlogRepository blogRepository,
                        IDesignRepository designRepository,
                        IDesignDetailRepository designDetailRepository,
                        IDesignPaymentRepository designPaymentRepository,
                        IRoomRepository roomRepository,
                        IVendorRepository vendorRepository,
                        IProductCategoryRepository productCategoryRepository)
        {
            _context = context;
            _systemUserAccountRepository = systemUserAccountRepository;
            _itemCategoryRepository = itemCategoryRepository;
            _decorItemRepository = decorItemRepository;
            _blogTagRepository = blogTagRepository;
            _blogRepository = blogRepository;
            _designRepository = designRepository;
            _designDetailRepository = designDetailRepository;
            _designPaymentRepository = designPaymentRepository;
            _roomRepository = roomRepository;
            _vendorRepository = vendorRepository;
            _productCategoryRepository = productCategoryRepository;
        }

        public ISystemUserAccountRepository SystemUserAccountRepository => _systemUserAccountRepository;
        public IItemCategoryRepository ItemCategoryRepository => _itemCategoryRepository;
        public IDecorItemRepository DecorItemRepository => _decorItemRepository;
        public IBlogTagRepository BlogTagRepository => _blogTagRepository;
        public IBlogRepository BlogRepository => _blogRepository;
        public IDesignRepository DesignRepository => _designRepository;
        public IDesignDetailRepository DesignDetailRepository => _designDetailRepository;
        public IDesignPaymentRepository DesignPaymentRepository => _designPaymentRepository;
        public IRoomRepository RoomRepository => _roomRepository;   
        public IVendorRepository VendorRepository => _vendorRepository;
        public IProductCategoryRepository ProductCategoryRepository => _productCategoryRepository;
        public async Task<int> SaveChangeAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task<IDbContextTransaction> BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
            return _transaction;
        }

        public async Task<int> CommitAsync()
        {
            if (_transaction == null)
            {
                return await _context.SaveChangesAsync();
            }

            try
            {
                int result = await _context.SaveChangesAsync();
                await _transaction.CommitAsync();

                return result;
            }
            catch
            {
                await RollbackAsync();
                throw;
            }
        }

        public async Task RollbackAsync()
        {
            await _transaction.RollbackAsync();
        }
        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
