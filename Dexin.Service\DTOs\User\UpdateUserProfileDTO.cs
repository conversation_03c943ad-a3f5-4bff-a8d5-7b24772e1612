﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.DTOs.User
{
    public class UpdateUserProfileDTO
    {
       // public int UserAccountId { get; set; }

        public string UserName { get; set; } = null!;

        public string FirstName { get; set; } = null!;

        public string LastName { get; set; } = null!;

        public string Email { get; set; } = null!;

        public string Phone { get; set; } = null!;
        public bool? Gender { get; set; }

        public bool IsActive { get; set; }
    }
}
