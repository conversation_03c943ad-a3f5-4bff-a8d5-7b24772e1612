﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DecorItemView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Dexin.Service.Implements
{
    public class DecorItemService : IDecorItemService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DecorItem> _logger;

        public DecorItemService(IUnitOfWork unitOfWork, ILogger<DecorItem> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }
        public async Task<ResponseModels<CreateDecorItemDTO>> CreateAsync(CreateDecorItemDTO createDecorItemDTO)
        {
            var response = new ResponseModels<CreateDecorItemDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                if (!await _unitOfWork.ItemCategoryRepository.CheckIdExisted(createDecorItemDTO.ItemCategoryId))
                {
                    response.Message = "Danh mục không tồn tại! Vui lòng thử lại!";
                    return response;
                }

                var model = createDecorItemDTO.FromCreateToDecorItem();
                var rs = await _unitOfWork.DecorItemRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Thêm mới thành công!";
                    response.Data = createDecorItemDTO;
                    return response;
                }
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Create Error:");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false,
            };

            try
            {
                var decorItem = await _unitOfWork.DecorItemRepository.GetByIdAsync(id);
                if (decorItem == null)
                {
                    response.Message = "Vật phẩm không tồn tại!";
                    return response;
                }

                var rs = await _unitOfWork.DecorItemRepository.RemoveAsync(decorItem);
                if (rs)
                {
                    response.Message = "Xóa thành công!";
                    response.Success = true;
                    response.Data = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Delete Error:");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DecorItemDTO>>> GetAllAsync()
        {
            var response = new ResponseModels<IEnumerable<DecorItemDTO>>()
            {
                Success = false,
                Data = null,
            };

            try { 
                var decorItems = await _unitOfWork.DecorItemRepository.GetAllIncludeAsync();
                if (decorItems == null)
                {
                    response.Message = "Không có dữ liệu!";
                    return response;
                }

                var rs = decorItems.Select(d => d.ToDecorItemDTO()).ToList();
                response.Success = true;
                response.Data = rs;
                response.Message = "Thành công!";
                return response;
            }
            catch (Exception ex) {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Error");
                return response;
            }
        }

        public async Task<ResponseModels<DecorItemDTO>> GetByIdAsync(int id)
        {
            var response = new ResponseModels<DecorItemDTO>()
            {
                Success = false,
                Data = null,
            };

            try
            {
                var decorItem = await _unitOfWork.DecorItemRepository.GetByIdIncludeAsync(id);
                if (decorItem == null)
                {
                    response.Message = "Không có dữ liệu!";
                    return response;
                }

                var rs = decorItem.ToDecorItemDTO();
                response.Success = true;
                response.Data = rs;
                response.Message = "Thành công!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Error");
                return response;
            }
        }

        public async Task<ResponseModels<UpdateDecorItemDTO>> UpdateAsync(int id, UpdateDecorItemDTO updateDecorItemDTO)
        {
            var response = new ResponseModels<UpdateDecorItemDTO>()
            {
                Success = false,
                Data = null,
            };

            try
            {

                var decorItem = await _unitOfWork.DecorItemRepository.GetByIdIncludeAsync(id);
                if (decorItem == null)
                {
                    response.Message = "Không tìm thấy sản phẩm!";
                    return response;
                }

                if(updateDecorItemDTO.ImageUrl.IsNullOrEmpty())
                {
                    updateDecorItemDTO.OriginWidth = decorItem.OriginWidth;
                    updateDecorItemDTO.OriginHeight = decorItem.OriginHeight;
                    updateDecorItemDTO.ImageUrl = decorItem.ImageUrl;
                }

                if (!await _unitOfWork.ItemCategoryRepository.CheckIdExisted(updateDecorItemDTO.ItemCategoryId))
                {
                    response.Message = "Không tìm thấy danh mục!";
                    return response;
                }

                var decorItemModel = updateDecorItemDTO.FromUpdateToDecorItem();
                decorItemModel.DecorItemId = id;
                decorItemModel.CreatedAt = decorItem.CreatedAt;

                if (await _unitOfWork.DecorItemRepository.UpdateAsync(decorItemModel) > 0)
                {
                    response.Message = "Cập nhật thành công!";
                    response.Success = true;
                    response.Data = updateDecorItemDTO;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra! Vui Lòng thử lại!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Error");
                return response;
            }
        }
    }
}
