﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Repository.Enums;

namespace Dexin.Repository.Interface
{
    public interface ISystemUserAccountRepository : IGenericRepository<SystemUserAccount>
    {
        Task<SystemUserAccount> GetUserByEmailAsync(string email);
        Task<IEnumerable<SystemUserAccount>> GetAllCustomersAsync();
        Task<IEnumerable<SystemUserAccount>> GetAllStaffAsync();
        Task<IEnumerable<SystemUserAccount>> GetAllVendorsAsync();
        Task<SystemUserAccount> GetUserByUserNameAsync(string userName);    
        Task<bool> CheckUserNameExisted(string userName);
        Task<bool> CheckEmailExisted(string email);
        Task<bool> CheckIdExisted(int id);

        // Dashboard methods
        Task<int> GetTotalUsersCountAsync();
        Task<int> GetNewUsersCountByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<int> GetActiveUsersCountAsync();
        Task<Dictionary<Role, int>> GetUserCountByRoleAsync();
        Task<IEnumerable<SystemUserAccount>> GetRecentUsersAsync(int limit);
        Task<int> GetUsersCountBeforeDateAsync(DateTime date);
    }
}
