﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface ISystemUserAccountRepository : IGenericRepository<SystemUserAccount>
    {
        Task<SystemUserAccount> GetUserByEmailAsync(string email);
        Task<IEnumerable<SystemUserAccount>> GetAllCustomersAsync();
        Task<IEnumerable<SystemUserAccount>> GetAllStaffAsync();
        Task<IEnumerable<SystemUserAccount>> GetAllVendorsAsync();
        Task<SystemUserAccount> GetUserByUserNameAsync(string userName);    
        Task<bool> CheckUserNameExisted(string userName);   
        Task<bool> CheckEmailExisted(string email);
        Task<bool> CheckIdExisted(int id);
    }
}
