using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DashboardView;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.Interface
{
    public interface IDashboardService
    {
        Task<ResponseModels<DashboardOverviewDTO>> GetDashboardOverviewAsync();
        Task<ResponseModels<UserStatisticsDTO>> GetUserStatisticsAsync(string period = "month");
        Task<ResponseModels<TransactionStatisticsDTO>> GetTransactionStatisticsAsync(string period = "month");
    }
}
