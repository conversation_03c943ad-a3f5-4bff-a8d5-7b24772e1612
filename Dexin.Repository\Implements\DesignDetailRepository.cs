﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class DesignDetailRepository : GenericRepository<DesignDetail>, IDesignDetailRepository
    {
        public DesignDetailRepository(DexinContext context) => _context = context;

        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.DesignDetails.AnyAsync(d => d.DesignDetailId == id);
        }

        public async Task<IEnumerable<DesignDetail>> GetAllIncludeAsync()
        {
            return await _context.DesignDetails
                .Include(d => d.Design)
                .Include(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                .ToListAsync();
        }

        public async Task<IEnumerable<DesignDetail>> GetByDesignIdIncludeAsync(int designId)
        {
            var details = await _context.DesignDetails
                .Include(d => d.Design)
                .Include(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                .Where(d => d.DesignId == designId)
                .ToListAsync();
            return details;
        }

        public async Task<DesignDetail> GetByIdIncludeAsync(int id)
        {
            return await _context.DesignDetails
                .Include(d => d.Design)
                .Include(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                .FirstOrDefaultAsync(d => d.DesignDetailId == id);

        }

    }
}
