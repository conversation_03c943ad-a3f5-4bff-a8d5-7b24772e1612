﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Dexin.Service.DTOs.BlogView
{
    public class CreateBlogWithImageRequest
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public IFormFile? ThumbnailUrl { get; set; }
        public string? Content { get; set; }
        public string? AspectRatio { get; set; }

        // For uploading images
        public List<IFormFile>? Images { get; set; }

        // For tagging blog posts
        public List<int?> TagIds { get; set; } 
    }
}
