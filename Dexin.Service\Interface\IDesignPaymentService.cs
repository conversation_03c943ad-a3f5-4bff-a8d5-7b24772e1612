﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DesignPaymentView;

namespace Dexin.Service.Interface
{
    public interface IDesignPaymentService
    {
        Task<ResponseModels< DesignPaymentDTO>> CreateAsync(CreateDesignPaymentDTO designPaymentDTO);
        Task<ResponseModels<DesignPaymentDTO>> UpdatePaymetnStatusAsync(string transactionIdGateway, string status);
        Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntAsync();
        Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntByTransactionIdAsync(string transactionId);
        Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntByDesignIdAsync(int designId);
        Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntByUserIdAsync(int userId);
        
        
    }
}
