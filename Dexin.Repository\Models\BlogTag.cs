﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class BlogTag
{
    public int BlogTagId { get; set; }

    public string? Name { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public virtual ICollection<BlogPostTag> BlogPostTags { get; set; } = new List<BlogPostTag>();
}
