﻿using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.Commons;
using Microsoft.IdentityModel.Tokens;

namespace Dexin.Service.Utils
{
    public static class GenerateJsonWebTokenString
    {
        public static string GenerateJsonWebToken(this SystemUserAccount account, JWTSection jwtSection, DateTime now)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSection.SecretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
            var claims = new[]
                    {
                        new Claim("UserId", account.UserAccountId.ToString()),
                        new Claim("UserName", account.UserName ?? account.Email),
                        new Claim("Role", account.RoleId.ToString())
                    };
            var token = new JwtSecurityToken(
                issuer: jwtSection.Issuer,
                audience: jwtSection.Audience,
                claims: claims,
                expires: now.AddMinutes(60),
                signingCredentials: credentials);


            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public static string GenerateJsonWebToken(this AdminAccount account, JWTSection jwtSection, DateTime now)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSection.SecretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
            var claims = new[]
                    {
                        new Claim("UserId", account.UseAccountId.ToString()),
                        new Claim("UserName", account.UserName),
                        new Claim("Role", account.Role.ToString())
                    };
            var token = new JwtSecurityToken(
                issuer: jwtSection.Issuer,
                audience: jwtSection.Audience,
                claims: claims,
                expires: now.AddHours(24),
                signingCredentials: credentials);


            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}
