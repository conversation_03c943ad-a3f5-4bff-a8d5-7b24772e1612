﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.User;

namespace Dexin.Service.DTOs.ChatView
{
    public class RoomDTO
    {
        public string RoomId { get; set; } = null!;

        public int? UserId { get; set; }

        public int? StaffId { get; set; }

        public DateTime? CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public bool IsActive { get; set; }

        public UserChatDTO? Staff { get; set; }

        public UserChatDTO? User { get; set; }
    }
}
