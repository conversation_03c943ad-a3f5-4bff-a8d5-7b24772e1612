﻿using Dexin.Service.DTOs.DesignView;
using Dexin.Service.Implements;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DesignPaymentsController : ControllerBase
    {
        private readonly IDesignPaymentService _designPaymentService;

        public DesignPaymentsController(IDesignPaymentService designPaymentService)
        {
            _designPaymentService = designPaymentService;
        }

        [HttpGet]
        [Authorize(Roles = "Manager, Staff")]
        public async Task<IActionResult> GetAllAsync()
        {
            var result = await _designPaymentService.GetAllDesingPayemntAsync();
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(result);
                }
                else
                {
                    return Ok(result);
                }
            }

            return BadRequest(result);

        }

        [HttpGet("get-all-by-degsignId/{designId}")]
        [Authorize]
        public async Task<IActionResult> GetAllByDesignIdAsync(int designId)
        {
            var result = await _designPaymentService.GetAllDesingPayemntByDesignIdAsync(designId);
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(result);
                }
                else
                {
                    return Ok(result);
                }
            }

            return BadRequest(result);

        }

        [HttpGet("get-all-by-userId/{userId}")]
        [Authorize(Roles = "Manager, Staff")]
        public async Task<IActionResult> GetAllByUserIdAsync(int userId)
        {
            var result = await _designPaymentService.GetAllDesingPayemntByUserIdAsync(userId);
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(result);
                }
                else
                {
                    return Ok(result);
                }
            }

            return BadRequest(result);

        }

        [HttpGet("get-all-by-tracsactionId/{transactionId}")]
        [Authorize(Roles = "Manager, Staff")]
        public async Task<IActionResult> GetAllByUserIdAsync(string transactionId)
        {
            var result = await _designPaymentService.GetAllDesingPayemntByTransactionIdAsync(transactionId);
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(result);
                }
                else
                {
                    return Ok(result);
                }
            }

            return BadRequest(result);

        }

        [HttpPut("update-design-payment-status")]
        [Authorize]
        public async Task<IActionResult> UpdateDesignPaymentStatusAsync(string transactionIdGateway, string status)
        {
            var result = await _designPaymentService.UpdatePaymetnStatusAsync(transactionIdGateway, status);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
