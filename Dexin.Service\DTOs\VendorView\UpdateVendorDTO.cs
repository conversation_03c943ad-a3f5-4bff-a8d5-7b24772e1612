﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Service.DTOs.VendorView
{
    public class UpdateVendorDTO
    {
        public int VendorId { get; set; }

        public string? CompanyName { get; set; }

        public string? LogoUrl { get; set; }

        public string? Description { get; set; }

        public string? ContactEmail { get; set; }

        public string? Address { get; set; }

        public string? TaxId { get; set; }

        public int? Status { get; set; }

        public int? UserAccountId { get; set; }

    }
}
