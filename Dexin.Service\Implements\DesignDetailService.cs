﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon.Runtime.Internal.Util;
using Dexin.Repository.Implements;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DecorItemView;
using Dexin.Service.DTOs.DesignDetailView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class DesignDetailService : IDesignDetailService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DesignDetail> _logger;

        public DesignDetailService(IUnitOfWork unitOfWork, ILogger<DesignDetail> logger)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        public async Task<ResponseModels<CreateDesignDetailDTO>> CreateAsync(int designId, CreateDesignDetailDTO dto)
        {
            var response = new ResponseModels<CreateDesignDetailDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = dto.FromCreateToDesignDetail();
                model.DesignId = designId;
                var rs = await _unitOfWork.DesignDetailRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Tạo mới thành công!";
                    response.Data = dto;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Create Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> CreateListAsync(int designId, List<CreateDesignDetailDTO> dtoList)
        {
            var response = new ResponseModels<bool>
            {
                Success = false,
                Data = false
            };

            try
            {
                if (dtoList == null || !dtoList.Any())
                {
                    response.Message = "Danh sách trống!";
                    return response;
                }

                foreach (var dto in dtoList)
                {
                    var model = dto.FromCreateToDesignDetail();
                    model.DesignId = designId; // Ensure correct association
                    await _unitOfWork.DesignDetailRepository.CreateAsync(model);
                }

                response.Success = true;
                response.Message = "Tạo danh sách thành công!";
                response.Data = true;
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại.";
                _logger.LogError(ex, "Create List Error:");
                return response;
            }
        }


        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false
            };

            try
            {
                var model = await _unitOfWork.DesignDetailRepository.GetByIdAsync(id);
                if (model == null)
                {
                    response.Message = "Đã có lỗi xảy ra";
                    return response;
                }

                if (await _unitOfWork.DesignDetailRepository.RemoveAsync(model))
                {
                    response.Success = true;
                    response.Message = "Xóa thành công!";
                    response.Data = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Delete Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignDetailDTO>>> GetAllAsync()
        {
            var response = new ResponseModels<IEnumerable<DesignDetailDTO>> ()
            {
                Success = false,
                Data = null
            };

            try
            {
                var items = await _unitOfWork.DesignDetailRepository.GetAllIncludeAsync();
                if (items == null)
                {
                    response.Message = "Không tìm thấy dữ liệu";
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Xóa thành công!";
                    response.Data = items.Select(i => i.ToDesignDetailDTO());
                    return response;
                }
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }

        }

        public async Task<ResponseModels<IEnumerable<DesignDetailDTO>>> GetByDesignIdAsync(int designId)
        {
            var response = new ResponseModels<IEnumerable<DesignDetailDTO>>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var items = await _unitOfWork.DesignDetailRepository.GetByDesignIdIncludeAsync(designId);
                if (items == null)
                {
                    response.Message = "Không tìm thấy dữ liệu";
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Xóa thành công!";
                    response.Data = items.Select(i => i.ToDesignDetailDTO());
                    return response;
                }
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<DesignDetailDTO>> GetByIdAsync(int id)
        {
            var response = new ResponseModels<DesignDetailDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var items = await _unitOfWork.DesignDetailRepository.GetByIdIncludeAsync(id);
                if (items == null)
                {
                    response.Message = "Không tìm thấy dữ liệu";
                    response.Success = true;
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Xóa thành công!";
                    response.Data = items.ToDesignDetailDTO();
                    return response;
                }
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Get By ID Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<UpdateDesignDetailDTO>> UpdateAsync(int designId, UpdateDesignDetailDTO dto)
        {
            var response = new ResponseModels<UpdateDesignDetailDTO>()
            {
                Success = false,
                Data = null,
            };

            try
            {

                var designDetail = await _unitOfWork.DesignDetailRepository.GetByIdIncludeAsync((int) dto.DesignDetailId);
                if (designDetail == null)
                {
                    response.Message = "Không tìm thấy!";
                    return response;
                }
          
                var updatedItem = dto.FromUpdateToDesignDetailDTO();
                updatedItem.CreatedAt = designDetail.CreatedAt;
                updatedItem.DesignId = designId;

                if (await _unitOfWork.DesignDetailRepository.UpdateAsync(updatedItem) > 0)
                {
                    response.Message = "Cập nhật thành công!";
                    response.Success = true;
                    response.Data = dto;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra! Vui Lòng thử lại!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Error");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> UpdateListByDesignIdAsync(int designId, List<UpdateDesignDetailDTO> updatedDetails)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false,
            };

            try
            {
                var existingDetails = await _unitOfWork.DesignDetailRepository.GetByDesignIdIncludeAsync(designId);
                if (existingDetails != null)
                {
                    foreach (var existing in existingDetails)
                    {
                        if (!updatedDetails.Any(u => u.DesignDetailId == existing.DesignDetailId))
                        {
                            await _unitOfWork.DesignDetailRepository.RemoveAsync(existing);
                        }
                    }

                    foreach (var updated in updatedDetails)
                    {
                        if (updated.DesignDetailId == 0 || updated.DesignDetailId == null)
                        {
                            await _unitOfWork.DesignDetailRepository.CreateAsync(updated.FromUpdateToDesignDetailDTO());
                        }
                        else
                        {
                            await _unitOfWork.DesignDetailRepository.UpdateAsync(updated.FromUpdateToDesignDetailDTO());
                        }
                    }

                    response.Success = true;
                    response.Message = "Cập nhật thành công!";
                    response.Data = true;
                    return response;
                } else
                {
                    response.Message = "Đã có lỗi xảy ra!";
                    return response;
                }
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Update List Error: ");
                return response;
            }
        }
    }
}
