﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;

namespace Dexin.Service.Utils
{
    public static class StatusTransfer
    {
        public static string ToVietNameseStatus(this ThreeDStatus threeDStatus)
        {
            switch (threeDStatus)
            {
                case ThreeDStatus.Unpaid:
                    return "Chưa trả";
                case ThreeDStatus.Paid:
                    return "Hoàn tất thanh toán";
                case ThreeDStatus.Cancelled:
                    return "Hủy";
                default:
                    return "Unknown";
            }
        }

        //public static ThreeDStatus ToThreeDStatus(this int status)
        //{
        //    switch (status)
        //    {
        //        case ThreeDStatus.Unpaid:
        //            return "Chưa trả";
        //        case ThreeDStatus.Paid:
        //            return "Hoàn tất thanh toán";
        //        case ThreeDStatus.Cancelled:
        //            return "Hủy";
        //        default:
        //            return "Unknown";
        //    }
        //}
    }
}
