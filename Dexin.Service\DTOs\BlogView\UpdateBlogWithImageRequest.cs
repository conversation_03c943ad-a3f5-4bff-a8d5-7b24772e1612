﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Dexin.Service.DTOs.BlogView
{
    public class UpdateBlogWithImageRequest
    {
        public string? Title { get; set; }

        public string? Subtitle { get; set; }

        public IFormFile? ThumbnailUrl { get; set; }

        public string? Content { get; set; }

        public int? Status { get; set; }

        public List<IFormFile>? Image { get; set; }
        public List<int?>? TagIds { get; set; } = new List<int?>();
    }
}
