﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface;

public interface IDesignPaymentRepository : IGenericRepository<DesignPayment>
{
    Task<IEnumerable<DesignPayment>> GetAllIncludeAsync();
    Task<IEnumerable<DesignPayment>> GetAllByCustomerIdInlude(int id);
    Task<DesignPayment> GetByIdIncludeAsync(int id);

    Task<DesignPayment> GetByTransactionIdAsync(string transactionId);

}
