﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="4.0.1.1" />
    <PackageReference Include="CloudinaryDotNet" Version="1.27.5" />
    <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="Google.Cloud.Firestore" Version="3.10.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="payOS" Version="1.0.9" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Dexin.Repository\Dexin.Repository.csproj" />
  </ItemGroup>

</Project>
