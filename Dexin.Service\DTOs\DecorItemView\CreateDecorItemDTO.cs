﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.DTOs.DecorItemView
{
    public class CreateDecorItemDTO
    {
        public string? Name { get; set; }

        public string? Description { get; set; }

        public string? ImageUrl { get; set; }

        public double? OriginWidth { get; set; }

        public double? OriginHeight { get; set; }

        public int? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int ItemCategoryId { get; set; }
    }
}
