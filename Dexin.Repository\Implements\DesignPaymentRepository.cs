﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class DesignPaymentRepository : GenericRepository<DesignPayment>, IDesignPaymentRepository
    {
        public DesignPaymentRepository(DexinContext context) => _context = context;
        public async Task<IEnumerable<DesignPayment>> GetAllByCustomerIdInlude(int userId)
        {
            return await _context.DesignPayments
                                 .Include(p => p.Design)
                                 .Include(p => p.UserAccount)
                                 .Where(p => p.UserAccountId == userId)
                                 .ToListAsync();
        }

        public async Task<IEnumerable<DesignPayment>> GetAllIncludeAsync()
        {
            return await _context.DesignPayments
                                .Include(p => p.Design)
                                .Include(p => p.UserAccount)
                                .ToListAsync();
        }

        public async Task<DesignPayment> GetByIdIncludeAsync(int id)
        {
            return await _context.DesignPayments
                                .Include(p => p.Design)
                                .Include(p => p.UserAccount)
                                .FirstOrDefaultAsync(p => p.DesignPaymentId == id);
        }

        public async Task<DesignPayment> GetByTransactionIdAsync(string transactionId)
        {
            return await _context.DesignPayments
                               .Include(p => p.Design)
                               .Include(p => p.UserAccount)
                               .FirstOrDefaultAsync(p => p.TransactionIdGateway.Equals(transactionId));
        }
    } 
}
