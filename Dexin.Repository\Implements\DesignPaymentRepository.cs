﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class DesignPaymentRepository : GenericRepository<DesignPayment>, IDesignPaymentRepository
    {
        public DesignPaymentRepository(DexinContext context) => _context = context;
        public async Task<IEnumerable<DesignPayment>> GetAllByCustomerIdInlude(int userId)
        {
            return await _context.DesignPayments
                                 .Include(p => p.Design)
                                 .Include(p => p.UserAccount)
                                 .Where(p => p.UserAccountId == userId)
                                 .ToListAsync();
        }

        public async Task<IEnumerable<DesignPayment>> GetAllIncludeAsync()
        {
            return await _context.DesignPayments
                                .Include(p => p.Design)
                                .Include(p => p.UserAccount)
                                .ToListAsync();
        }

        public async Task<DesignPayment> GetByIdIncludeAsync(int id)
        {
            return await _context.DesignPayments
                                .Include(p => p.Design)
                                .Include(p => p.UserAccount)
                                .FirstOrDefaultAsync(p => p.DesignPaymentId == id);
        }

        public async Task<DesignPayment> GetByTransactionIdAsync(string transactionId)
        {
            return await _context.DesignPayments
                               .Include(p => p.Design)
                               .Include(p => p.UserAccount)
                               .FirstOrDefaultAsync(p => p.TransactionIdGateway.Equals(transactionId));
        }

        // Dashboard methods
        public async Task<decimal> GetTotalRevenueAsync()
        {
            return await _context.DesignPayments
                .Where(p => p.Amount.HasValue && p.Status == "PAID")
                .SumAsync(p => p.Amount.Value);
        }

        public async Task<decimal> GetRevenueByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.DesignPayments
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt < endDate &&
                           p.Amount.HasValue && p.Status == "PAID")
                .SumAsync(p => p.Amount.Value);
        }

        public async Task<int> GetTotalPaymentsCountAsync()
        {
            return await _context.DesignPayments.CountAsync();
        }

        public async Task<int> GetPaymentCountByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.DesignPayments
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt < endDate)
                .CountAsync();
        }

        public async Task<int> GetPaymentCountByStatusAsync(string status)
        {
            return await _context.DesignPayments
                .Where(p => p.Status == status)
                .CountAsync();
        }
    }
}
