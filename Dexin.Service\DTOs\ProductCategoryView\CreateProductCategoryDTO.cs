﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.DTOs.ProductCategoryView
{
    public class CreateProductCategoryDTO
    {
        public string? Name { get; set; }

        public string? Description { get; set; }

        public int? Status { get; set; }

        //public DateTime? CreatedAt { get; set; }

        //public DateTime? ModifiedAt { get; set; }

       // public int? UserAccountId { get; set; }

       // public int? ParentId { get; set; }
    }
}
