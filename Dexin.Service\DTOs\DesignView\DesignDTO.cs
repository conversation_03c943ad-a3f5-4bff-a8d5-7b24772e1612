﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.DesignDetailView;
using Dexin.Service.DTOs.User;

namespace Dexin.Service.DTOs.DesignView
{
    public class DesignDTO
    {
        public int DesignId { get; set; }

        public bool? Type { get; set; }

        public string? Title { get; set; }

        public string? PathUrl { get; set; }

        public string? ThumbnailUrl { get; set; }

        public double? CanvasScale { get; set; }

        public double? CanvasPosX { get; set; }

        public double? CanvasPosY { get; set; }

        public string? Note { get; set; }

        public long? Price { get; set; }
        public string? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public int? CreatedBy { get; set; }

     //   public int? StaffId { get; set; }

        public  UserProfileDTO? Customer { get; set; }

        public  List<DesignDetailDTO> DesignDetails { get; set; } = new List<DesignDetailDTO>();

        public  List<DesignPayment> DesignPayments { get; set; } = new List<DesignPayment>();

        public UserProfileDTO? Staff { get; set; }
    }
}
