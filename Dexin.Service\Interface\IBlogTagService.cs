﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.BlogTagView;

namespace Dexin.Service.Interface
{
    public interface IBlogTagService
    {
        Task<ResponseModels<IEnumerable<BlogTagDTO>>> GetAllAsync();
        Task<ResponseModels<IEnumerable<BlogTagDTO>>> SearchByNameAsync(string name);
        Task<ResponseModels<BlogTagDTO>> GetByIdAsync(int id);
        Task<ResponseModels<UpdateBlogTagDTO>> UpdateByBlogTagAsync(int id, UpdateBlogTagDTO blogTagDTO);
        Task<ResponseModels<bool>> DeleteAsync(int id);
        Task<ResponseModels<CreateBlogTagDTO>> CreateAsync(CreateBlogTagDTO blogTagDTO);    
    }
}
