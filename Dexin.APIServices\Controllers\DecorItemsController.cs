﻿using Azure.Core;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DecorItemView;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Drawing;
using static System.Net.Mime.MediaTypeNames;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DecorItemsController : ControllerBase
    {
        private readonly IDecorItemService _decorItemService;
        private readonly IImageService _imageService;

        public DecorItemsController(IDecorItemService decorItemService, IImageService imageService)
        {
            _decorItemService = decorItemService;
            _imageService = imageService;   
        }
        // POST: api/DecorItems
        [HttpPost]
        public async Task<ActionResult<ResponseModels<CreateDecorItemDTO>>> CreateAsync([FromForm] CreateDecorItemWithImageRequest request)
        {
            string imageUrl = await _imageService.UploadImageAsync(request.ImageFile);

            // Get image dimensions from IFormFile
            double originWidth, originHeight;
            using (var image = System.Drawing.Image.FromStream(request.ImageFile.OpenReadStream()))
            {
                originWidth = image.Width;
                originHeight = image.Height;
            }

            var dto = new CreateDecorItemDTO
            {
                Name = request.Name,
                Description = request.Description,
                OriginWidth = originWidth,
                OriginHeight = originHeight,
                Status = request.Status,
                CreatedAt = request.CreatedAt,
                ItemCategoryId = request.ItemCategoryId,
                ImageUrl = imageUrl
            };

            var response = await _decorItemService.CreateAsync(dto);
            if (!response.Success)
            {
                return BadRequest(response); // Handle bad requests appropriately
            }

            return Ok(response);
        }

        // GET: api/DecorItems
        [HttpGet]
        public async Task<ActionResult<ResponseModels<IEnumerable<DecorItemDTO>>>> GetAllAsync()
        {
            var response = await _decorItemService.GetAllAsync();
            if (!response.Success)
            {
                return NotFound(response); // Handle not found cases appropriately
            }

            return Ok(response);
        }

        // GET: api/DecorItems/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ResponseModels<DecorItemDTO>>> GetByIdAsync(int id)
        {
            var response = await _decorItemService.GetByIdAsync(id);
            if (!response.Success)
            {
                return NotFound(response); // Handle not found cases appropriately
            }

            return Ok(response);
        }

        // PUT: api/DecorItems/5
        [HttpPut("{id}")]
        public async Task<ActionResult<ResponseModels<UpdateDecorItemDTO>>> UpdateAsync(int id, [FromForm] UpdateDecorItemWithImageRequest request)
        {
            double originWidth = 0, originHeight = 0;
            string imageUrl = "";
            if (request.ImageFile != null)
            {
                imageUrl = await _imageService.UploadImageAsync(request.ImageFile);

                // Get image dimensions from IFormFile
               
                using (var image = System.Drawing.Image.FromStream(request.ImageFile.OpenReadStream()))
                {
                    originWidth = image.Width;
                    originHeight = image.Height;
                }
            }
            var updateDecorItemDTO = new UpdateDecorItemDTO
            {
                Name = request.Name,
                Description = request.Description,
                OriginWidth = originWidth,
                OriginHeight = originHeight,
                Status = request.Status,
                CreatedAt = DateTime.Now,
                ItemCategoryId = request.ItemCategoryId,
                ImageUrl = imageUrl
            };

            var response = await _decorItemService.UpdateAsync(id, updateDecorItemDTO);
            if (!response.Success)
            {
                return BadRequest(response); // Handle bad requests or not found cases appropriately
            }

            return Ok(response);
        }

        // DELETE: api/DecorItems/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<ResponseModels<bool>>> DeleteAsync(int id)
        {
            var response = await _decorItemService.DeleteAsync(id);
            if (!response.Success)
            {
                return NotFound(response); // Handle not found cases appropriately
            }

            return Ok(response);
        }

    }
}
