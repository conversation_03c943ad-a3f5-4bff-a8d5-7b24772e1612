﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Service.DTOs.BlogTagView
{
    public class BlogTagDTO
    {
        public int BlogTagId { get; set; }

        public string? Name { get; set; }

        public int? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public List<BlogPostTag> BlogPostTags { get; set; } = new List<BlogPostTag>();
    }
}
