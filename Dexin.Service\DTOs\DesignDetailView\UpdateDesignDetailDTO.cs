﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Service.DTOs.DesignDetailView
{
    public class UpdateDesignDetailDTO
    {
        public int? DesignDetailId { get; set; }
        public double? PositionX { get; set; }

        public double? PositionY { get; set; }

        public double? Width { get; set; }

        public double? Height { get; set; }

        public double? Rotation { get; set; }

        public int? Layer { get; set; }

        public int? Status { get; set; }
        //public int? DesignId { get; set; }

        public int? DecorItemId { get; set; }
    }
}
