﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class BlogLike
{
    public int BlogLikeId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? UserAccountId { get; set; }

    public int? BlogPostId { get; set; }

    public virtual Blog? BlogPost { get; set; }

    public virtual SystemUserAccount? UserAccount { get; set; }
}
