﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.ChatView;

namespace Dexin.Service.Mappers
{
    public static class RoomMappers
    {
        public static Room FromCreateToRoom(this CreateRoomDTO dto)
        {
            return new Room()
            {
                RoomId = dto.RoomId,
                StaffId = dto.StaffId,
                UserId = dto.UserId,
            };
        }

        public static RoomDTO ToRoomDTO(this Room room) 
        { 
            return new RoomDTO()
            {
                RoomId = room.RoomId,
                CreatedDate = room.CreatedDate,
                IsActive = room.IsActive,
                ModifiedDate = room.ModifiedDate,
                Staff = room.Staff.ToUserChatDTO(),
                User = room.User.ToUserChatDTO(),
                UserId = room.UserId,
                StaffId = room.StaffId,
            };
        }
    }
}
