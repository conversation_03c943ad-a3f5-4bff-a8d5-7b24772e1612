﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Enums;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class SystemUserAccountRepository : GenericRepository<SystemUserAccount>, ISystemUserAccountRepository
    {
        public SystemUserAccountRepository(){}

        public SystemUserAccountRepository(DexinContext context) => _context = context;

        public async Task<SystemUserAccount> GetUserByEmailAsync(string email)
        {
            return await  _context.SystemUserAccounts.FirstOrDefaultAsync(a => a.Email == email);
        }

        public async Task<SystemUserAccount> GetUserByUserNameAsync(string userName)
        {
            return await _context.SystemUserAccounts.FirstOrDefaultAsync(a => a.UserName == userName);
        }
        public async Task<bool> CheckEmailExisted(string email)
        {
            return await _context.SystemUserAccounts.AnyAsync(a => a.Email == email);
        }

        public async Task<bool> CheckUserNameExisted(string userName)
        {
            return await _context.SystemUserAccounts.AnyAsync(a => a.UserName == userName);
        }

        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.SystemUserAccounts.AnyAsync(a => a.UserAccountId == id);
        }

        public async Task<IEnumerable<SystemUserAccount>> GetAllCustomersAsync()
        {
           return await _context.SystemUserAccounts.Where(a => a.RoleId == Role.User).ToListAsync();
        }

        public async Task<IEnumerable<SystemUserAccount>> GetAllStaffAsync()
        {
            return await _context.SystemUserAccounts.Where(a => a.RoleId == Role.Staff).ToListAsync();
        }

        public async Task<IEnumerable<SystemUserAccount>> GetAllVendorsAsync()
        {
            return await _context.SystemUserAccounts.Where(a => a.RoleId == Role.Vendor).ToListAsync();
        }

        // Dashboard methods
        public async Task<int> GetTotalUsersCountAsync()
        {
            return await _context.SystemUserAccounts.CountAsync();
        }

        public async Task<int> GetNewUsersCountByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.SystemUserAccounts
                .Where(u => u.CreatedDate >= startDate && u.CreatedDate < endDate)
                .CountAsync();
        }

        public async Task<int> GetActiveUsersCountAsync()
        {
            return await _context.SystemUserAccounts
                .Where(u => u.IsActive)
                .CountAsync();
        }

        public async Task<Dictionary<Role, int>> GetUserCountByRoleAsync()
        {
            var result = await _context.SystemUserAccounts
                .GroupBy(u => u.RoleId)
                .Select(g => new { Role = g.Key, Count = g.Count() })
                .ToListAsync();

            return result.ToDictionary(x => x.Role, x => x.Count);
        }

        public async Task<IEnumerable<SystemUserAccount>> GetRecentUsersAsync(int limit)
        {
            return await _context.SystemUserAccounts
                .OrderByDescending(u => u.CreatedDate)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<int> GetUsersCountBeforeDateAsync(DateTime date)
        {
            return await _context.SystemUserAccounts
                .Where(u => u.CreatedDate < date)
                .CountAsync();
        }
    }
}
