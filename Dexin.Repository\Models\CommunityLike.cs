﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class CommunityLike
{
    public int CommunityLikeId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? UserAccountId { get; set; }

    public int? CommunityPostId { get; set; }

    public virtual CommunityPost? CommunityPost { get; set; }

    public virtual SystemUserAccount? UserAccount { get; set; }
}
