﻿using Dexin.Repository.Implements;
using Dexin.Repository.Interface;
using Dexin.Repository;
using Dexin.Service.Implements;
using Dexin.Service.Interface;

namespace Dexin.APIServices
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddWebAPIService(this IServiceCollection services, IConfiguration config)
        {
            services.AddScoped<ISystemUserAccountRepository, SystemUserAccountRepository>();
            services.AddScoped<ISystemUserAccountService, SystemUserAccountService>();

            services.AddScoped<IItemCategoryRepository, ItemCategoryRepository>();
            services.AddScoped<IItemCategoryService, ItemCategoryService>();

            services.AddScoped<IImageService, ImageService>();

            services.AddScoped<IDecorItemRepository, DecorItemRepository>();
            services.AddScoped<IDecorItemService, DecorItemService>();

            services.AddScoped<IBlogTagRepository, BlogTagRepository>();
            services.AddScoped<IBlogTagService, BlogTagService>();

            services.AddScoped<IBlogRepository, BlogRepository>();
            services.AddScoped<IBlogService, BlogService>();

            services.AddScoped<IDesignDetailRepository, DesignDetailRepository>();
            services.AddScoped<IDesignDetailService, DesignDetailService>();

            services.AddScoped<IDesignRepository, DesignRepository>();
            services.AddScoped<IDesignService, DesignService>();

            services.AddScoped<IDesignPaymentRepository, DesignPaymentRepository>();
            services.AddScoped<IDesignPaymentService, DesignPaymentService>();

            services.AddScoped<IRoomRepository, RoomRepository>();
            services.AddScoped<IRoomService, RoomService>();

            services.AddScoped<IVendorRepository, VendorRepository>();
            services.AddScoped<IVendorService, VendorService>();

            services.AddScoped<IProductCategoryRepository, ProductCategoryRepository>();

            services.AddScoped<IOrderRepository, OrderRepository>();
            services.AddScoped<IProductPaymentRepository, ProductPaymentRepository>();
            services.AddScoped<IReviewRepository, ReviewRepository>();

            services.AddScoped<IDashboardService, DashboardService>();

            services.AddScoped<IClaimsService, ClaimsService>();

            services.AddScoped<ITokenBlacklistService, TokenBlacklistService>();

            services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
            services.AddScoped<ICurrentTime, CurrentTime>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            return services;    
        }
    }
}
