﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class RoomRepository : GenericRepository<Room>, IRoomRepository
    {
        public RoomRepository(DexinContext context) => _context = context;
        public async Task<bool> ChekIdExisted(string id)
        {
            return await _context.Rooms.AnyAsync(r => r.RoomId.Equals(id));
        }

        public async Task<IEnumerable<Room>> GetAllByCustomerIdIncludeAsync(int customerId)
        {
          return await _context.Rooms.Include(r => r.User)
                                     .Include(r => r.Staff)
                                     .Where(r => r.UserId == customerId)
                                     .ToListAsync();   
        }

        public async Task<IEnumerable<Room>> GetAllByStaffIdIncludeAsync(int staffId)
        {
            return await _context.Rooms.Include(r => r.User)
                                       .Include(r => r.Staff)
                                       .Where(r => r.StaffId == staffId)
                                       .ToListAsync();
        }

        public async Task<IEnumerable<Room>> GetAllIncludeAsync()
        {
            return await _context.Rooms.Include(r => r.User)
                                       .Include(r => r.Staff)
                                       .ToListAsync();
        }

        public async Task<Room> GetByIdIncludeAsync(string id)
        {
            return await _context.Rooms.Include(r => r.User)
                                       .Include(r => r.Staff)
                                       .FirstOrDefaultAsync(r => r.RoomId == id);
        }
    }
}
