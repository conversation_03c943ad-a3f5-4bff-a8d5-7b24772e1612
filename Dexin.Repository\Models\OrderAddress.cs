﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class OrderAddress
{
    public int OrderAddressId { get; set; }

    public string? AddressLine { get; set; }

    public string? Ward { get; set; }

    public string? District { get; set; }

    public string? City { get; set; }

    public string? Country { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? UserAccountId { get; set; }

    public virtual ICollection<Order> Orders { get; set; } = new List<Order>();

    public virtual SystemUserAccount? UserAccount { get; set; }
}
