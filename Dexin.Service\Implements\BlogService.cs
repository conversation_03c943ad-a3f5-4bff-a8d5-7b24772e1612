﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.BlogView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class BlogService : IBlogService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<Blog> _logger;

        public BlogService(IUnitOfWork unitOfWork, ILogger<Blog> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }
        public async Task<ResponseModels<CreateBlogDTO>> CreateAsync(CreateBlogDTO createBlogDTO, string userName)
        {
            var response = new ResponseModels<CreateBlogDTO>()
            {
                Data = null,
                Success = false,
            };
            try

            {

                var user = await _unitOfWork.SystemUserAccountRepository.GetUserByEmailAsync(userName);
                if (user == null)
                {
                    user = await _unitOfWork.SystemUserAccountRepository.GetUserByUserNameAsync(userName);
                }
                var blog = createBlogDTO.FromCreateToBlog(user);
                var rs = await _unitOfWork.BlogRepository.CreateAsync(blog);
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Thêm mới bài viết thành công!";
                    response.Data = createBlogDTO;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Create Error: ");
                return response;

            }
        }

        public async Task<ResponseModels<IEnumerable<BlogDTO>>> GetAllAync()
        {
            var response = new ResponseModels<IEnumerable<BlogDTO>>()
            {
                Data = null,
                Success = false,
            };

            try
            {
                var rs = await _unitOfWork.BlogRepository.GetAllInCLudeAsync();
                if (rs == null)
                {
                    response.Message = "Không có dữ liệu!";
                    return response;

                }

                response.Success = true;
                response.Message = "Thành công!";
                response.Data = rs.Select(r => r.ToBlogDTO());
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Không có dữ liệu!";
                _logger.LogError(ex, "GetAll Error: ");
                return response;

            }

        }

        public Task<ResponseModels<IEnumerable<BlogDTO>>> SearchAsync(string? title, string? subtitle, string? content)
        {
            throw new NotImplementedException();
        }

        public async Task<ResponseModels<UpdateBlogDTO>> UpdateAsync(int id, UpdateBlogDTO updateBlogDTO)
        {
            var response = new ResponseModels<UpdateBlogDTO>()
            {
                Data = null,
                Success = false,
            };
            try
            {
                var blog = await _unitOfWork.BlogRepository.GetByIdIncludeAsync(id);
                if(blog == null)
                {
                    response.Message = "Không tìm thấy bài viết!";
                    return response;
                }

                var updateModel = updateBlogDTO.FromUpdateToBlog();

                updateModel.BlogPostId = id;
                updateModel.AspectRatio = blog.AspectRatio;
                updateModel.Likes = blog.Likes;
                updateModel.Views = blog.Views;
                updateModel.CreatedAt = blog.CreatedAt;
                updateModel.UserAccountId = blog.UserAccountId;
                updateModel.BlogLikes = blog.BlogLikes;

                if(updateModel.BlogImages == null)
                {
                    updateModel.BlogImages = blog.BlogImages;
                }

                if(updateModel.BlogPostTags == null)
                {
                    updateModel.BlogPostTags = blog.BlogPostTags;
                }

                if (string.IsNullOrEmpty(updateModel.ThumbnailUrl))
                {
                    updateModel.ThumbnailUrl = blog.ThumbnailUrl;
                }

                var rs = await _unitOfWork.BlogRepository.UpdateAsync(updateModel);

                if(rs > 0)
                {
                    response.Success = true;
                    response.Message = "Cập nhật bài viết thành công!";
                    response.Data = updateBlogDTO;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra! ";
                return response;

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Update Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false,
            };

            try
            {
                var blog = await _unitOfWork.BlogRepository.GetByIdIncludeAsync(id);
                if (blog == null)
                {
                    response.Message = "Vật phẩm không tồn tại!";
                    return response;
                }

                var rs = await _unitOfWork.BlogRepository.RemoveAsync(blog);
                if (rs)
                {
                    response.Message = "Xóa thành công!";
                    response.Success = true;
                    response.Data = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Delete Error:");
                return response;
            }
        }
    }
}
