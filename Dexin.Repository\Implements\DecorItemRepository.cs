﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class DecorItemRepository : GenericRepository<DecorItem>, IDecorItemRepository
    {
        public DecorItemRepository() { }

        public DecorItemRepository(DexinContext context) => _context = context; 
        public async Task<IEnumerable<DecorItem>> GetAllIncludeAsync()
        {
            return await _context.DecorItems.Include(d => d.ItemCategory).ToListAsync();
        }

        public async Task<DecorItem> GetByIdIncludeAsync(int id)
        {
            var item = await _context.DecorItems.Include(d => d.ItemCategory).FirstOrDefaultAsync(d => d.DecorItemId == id);
            return item ?? new DecorItem();
        }

        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.DecorItems.AnyAsync(d => d.DecorItemId == id);
        }
    }
}
