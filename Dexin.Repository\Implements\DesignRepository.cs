﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class DesignRepository : GenericRepository<Design>, IDesignRepository
    {
        public DesignRepository(DexinContext context) => _context = context;

        public async Task<bool> CheckExistedId(int id)
        {
            return await _context.Designs.AnyAsync(d => d.DesignId == id);
        }

        public async Task<IEnumerable<Design>> GetAllByStaffIdAsync(int id)
        {
            return await _context.Designs.Include(d => d.DesignDetails).ThenInclude(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                         .Include(d => d.DesignPayments)
                         .Include(d => d.CreatedByNavigation)
                         .Include(d => d.Staff)
                         .Where(d => d.StaffId == id)
                        .ToListAsync();
        }

        public async Task<IEnumerable<Design>> GetAllByUserAndStaffIdAsync(int staffId, int userId)
        {
            return await _context.Designs.Include(d => d.DesignDetails).ThenInclude(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                         .Include(d => d.DesignPayments)
                         .Include(d => d.CreatedByNavigation)
                         .Include(d => d.Staff)
                         .Where(d => d.StaffId == staffId && d.CreatedBy == userId)
                         .ToListAsync();
        }

        public async Task<IEnumerable<Design>> GetAllByUserIdAsync(int id)
        {
            return await _context.Designs
                         .Include(d => d.DesignDetails).ThenInclude(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                         .Include(d => d.DesignPayments)
                         .Include(d => d.CreatedByNavigation)
                         .Include(d => d.Staff)
                         .Where(d => d.CreatedBy == id)
                         .ToListAsync();
        }

        public async Task<IEnumerable<Design>> GetAllIncludeAsync()
        {
            return await _context.Designs
                         .Include(d => d.DesignDetails).ThenInclude(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                         .Include(d => d.DesignPayments)
                         .Include(d => d.CreatedByNavigation)
                         .Include(d => d.Staff)
                         .ToListAsync();
        }

        public async Task<Design> GetByIdIncludeAsync(int id)
        {
            return await _context.Designs
                         .Include(d => d.DesignDetails).ThenInclude(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                         .Include(d => d.DesignPayments)
                         .Include(d => d.CreatedByNavigation)
                         .Include(d => d.Staff)
                         .FirstOrDefaultAsync(d => d.DesignId == id) ?? new Design();
        }

        public async Task<IEnumerable<Design>> GetByTypeAsync(bool type)
        {
            var design = await _context.Designs
                       .Include(d => d.DesignDetails).ThenInclude(d => d.DecorItem).ThenInclude(d => d.ItemCategory)
                       .Include(d => d.DesignPayments)
                       .Include(d => d.CreatedByNavigation)
                       .Include(d => d.Staff)
                       .Where(d => d.Type == type)
                       .ToListAsync();
            return design;
        }
    }
}
