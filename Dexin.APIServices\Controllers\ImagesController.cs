﻿using Dexin.Service.DTOs.ImageModel;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations; // Ensure this is present if you use SwaggerOperation

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ImagesController : ControllerBase
    {
        private readonly IImageService _imageService;

        public ImagesController(IImageService imageService)
        {
            _imageService = imageService;
        }

        [HttpPost("upload")]
        [Consumes("multipart/form-data")] // Keep this attribute
        public async Task<IActionResult> UploadImage([FromForm] ImageUploadRequestModel model) // Changed parameter
        {
            if (model == null || model.File == null) // Updated check
            {
                return BadRequest("No file uploaded.");
            }

            var imageUrl = await _imageService.UploadImageAsync(model.File); // Access file from model

            return Ok(new { Url = imageUrl });
        }

        [HttpPost("upload-multiple")]
        [SwaggerOperation(Summary = "Upload multiple images")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> UploadImages([FromForm] List<IFormFile> files)
        {
            if (files == null || !files.Any())
            {
                return BadRequest("No files uploaded.");
            }

            var imageUrls = await _imageService.UploadImagesAsync(files);

            return Ok(new { Urls = imageUrls });
        }

        [HttpPost("upload-obj")]
        [Consumes("multipart/form-data")]
        [RequestSizeLimit(long.MaxValue)]
        [DisableRequestSizeLimit]
        public async Task<IActionResult> UploadFile([FromForm] ImageUploadRequestModel model)
        {
            if (model == null || model.File == null)
            {
                return BadRequest("No file uploaded.");
            }

            var imageUrl = await _imageService.UploadLargeFileAsync(model.File);

            return Ok(new { Url = imageUrl });
        }

    }
}