﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class ProductCategoryRepository : GenericRepository<ProductCategory>, IProductCategoryRepository
    {
        public ProductCategoryRepository(DexinContext context)
        {
            _context = context; 
        }
        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.ProductCategories.AnyAsync(p => p.ProductCategoryId == id);
        }

        public async Task<bool> CheckNameExisted(string name)
        {
            return await _context.ProductCategories.AnyAsync(p => p.Name.ToLower().Equals(name.ToLower()));
        }

        public async Task<IEnumerable<ProductCategory>> GetAllIncludeAsync()
        {
            var items = await _context.ProductCategories.Include(p => p.Products).ToListAsync();

            return items ?? new List<ProductCategory>();
        }
        public async Task<ProductCategory> GetByIdInclude(int id)
        {
            var item = await _context.ProductCategories.Include(p => p.Products).FirstOrDefaultAsync(p => p.ProductCategoryId == id);

            return item;
        }
    }
}
