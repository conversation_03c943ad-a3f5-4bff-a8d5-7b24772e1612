﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Service.DTOs.DesignPaymentView
{
    public class DesignPaymentDTO
    {
        public int DesignPaymentId { get; set; }

        public string? PaymentMethod { get; set; }

        public string? PaymentProvide { get; set; }

        public decimal? Amount { get; set; }

        public string? CurrencyCode { get; set; }

        public string? TransactionIdGateway { get; set; }

        public DateTime? PaymentDate { get; set; }

        public string? GatewayResponse { get; set; }

        public string? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public int? DesignId { get; set; }

        public int? UserAccountId { get; set; }

        public  Design? Design { get; set; }

        public  SystemUserAccount? UserAccount { get; set; }
    }
}
