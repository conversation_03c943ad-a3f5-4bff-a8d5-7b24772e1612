﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class DesignDetail
{
    public int DesignDetailId { get; set; }

    public double? PositionX { get; set; }

    public double? PositionY { get; set; }

    public double? Width { get; set; }

    public double? Height { get; set; }

    public double? Rotation { get; set; }

    public int? Layer { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? DesignId { get; set; }

    public int? DecorItemId { get; set; }

    public virtual DecorItem? DecorItem { get; set; }

    public virtual Design? Design { get; set; }
}
