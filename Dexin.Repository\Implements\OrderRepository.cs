using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Repository.Implements
{
    public class OrderRepository : GenericRepository<Order>, IOrderRepository
    {
        public OrderRepository(DexinContext context) => _context = context;

        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.Orders.AnyAsync(o => o.OrdersId == id);
        }

        public async Task<IEnumerable<Order>> GetAllIncludeAsync()
        {
            return await _context.Orders
                .Include(o => o.UserAccount)
                .Include(o => o.OrderAddress)
                .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                .Include(o => o.ProductPayments)
                .ToListAsync();
        }

        public async Task<Order> GetByIdIncludeAsync(int id)
        {
            return await _context.Orders
                .Include(o => o.UserAccount)
                .Include(o => o.OrderAddress)
                .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                .Include(o => o.ProductPayments)
                .FirstOrDefaultAsync(o => o.OrdersId == id) ?? new Order();
        }

        public async Task<IEnumerable<Order>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Orders
                .Where(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate)
                .Include(o => o.UserAccount)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Order>> GetOrdersByUserIdAsync(int userId)
        {
            return await _context.Orders
                .Where(o => o.UserAccountId == userId)
                .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Order>> GetRecentOrdersAsync(int limit)
        {
            return await _context.Orders
                .Include(o => o.UserAccount)
                .OrderByDescending(o => o.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<decimal> GetRevenueByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Orders
                .Where(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate && o.TotalAmount.HasValue)
                .SumAsync(o => o.TotalAmount.Value);
        }

        public async Task<int> GetTotalOrdersCountAsync()
        {
            return await _context.Orders.CountAsync();
        }

        public async Task<decimal> GetTotalRevenueAsync()
        {
            return await _context.Orders
                .Where(o => o.TotalAmount.HasValue)
                .SumAsync(o => o.TotalAmount.Value);
        }
    }
}
