﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IDesignRepository : IGenericRepository<Design>
    {
        Task<IEnumerable<Design>> GetAllIncludeAsync();
        Task<Design> GetByIdIncludeAsync(int id);
        Task<bool> CheckExistedId(int id);  
        Task<IEnumerable<Design>> GetByTypeAsync(bool type);
        Task<IEnumerable<Design>> GetAllByUserIdAsync(int id);
        Task<IEnumerable<Design>> GetAllByStaffIdAsync(int id);
        Task<IEnumerable<Design>> GetAllByUserAndStaffIdAsync(int staffId, int userId);

        // Dashboard methods
        Task<int> GetTotalDesignsCountAsync();
    }
}
