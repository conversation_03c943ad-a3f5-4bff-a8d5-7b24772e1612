{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "AllowedHosts": "*",

    "ConnectionStrings": {
        // "DefaultConnection": "Data Source=localhost;Initial Catalog=DEXIN;User ID=sa;Password=*********;Integrated Security=True;Encrypt=True;Trust Server Certificate=True"
        // "DefaultConnection": "Server=DexinDB.mssql.somee.com,1433;Database=DexinDB;User Id=Hugo194_SQLLogin_1;Password=**********;Encrypt=True;TrustServerCertificate=True",
        "DefaultConnection": "workstation id=DBDexin.mssql.somee.com, 1433;packet size=4096;user id=Hugo194_SQLLogin_1;pwd=**********;data source=DBDexin.mssql.somee.com;persist security info=False;initial catalog=DBDexin;TrustServerCertificate=True"
        //"DefaultConnection": "Data Source=localhost;Initial Catalog=DBDexin;User ID=sa;Password=*********;Integrated Security=True;Encrypt=True;Trust Server Certificate=True"
    },

    "JWTSection": {
        "Issuer": "https://dexin.onrender.com",
        "Audience": "https://dexin.onrender.com",
        "SecretKey": "0ccfeb299b126a479a64630e2d34e9e91e5fcbcaea8ac9e3347e224b0557a53e"
    },

    "CloudinarySetting": {
        "CloudName": "djwlwxtah",
        "ApiKey": "592824431885486",
        "ApiSecret": "qyqknu35yj6wGoxI4dZ52InA_Yo"
    },
    "AWS": {
        "BucketName": "dexin-bucket",
        "Region": "ap-southeast-2",
        "AccessKey": "********************",
        "SecretKey": "GuCmwJjY+5nqbkBCmE92WxlJr51PyzFOzLppZEh8"
    },
    "PayOS": {
        "ClientId": "19a69df5-c987-4b30-8ac0-71947fb75d68",
        "ApiKey": "01933da5-306e-4419-bb77-a8f72c1ed2e5",
        "ChecksumKey": "ec79bfa26fe2d57ec80c3c438a55263925634c00d2be6d55e34eb101a9387293"
    },

    "AdminAccount": {
        "UseAccountId": "0",
        "Name": "DEXIN",
        "Email": "<EMAIL>",
        "Role": "Manager",
        "UserName": "SystemManager",
        "Password": "@@abc123@@"
    },

    "Firebase": {
        "ProjectId": "dexin-bf7c0",
        "CredentialPath": "firebase-service-account.json"
    }
}
