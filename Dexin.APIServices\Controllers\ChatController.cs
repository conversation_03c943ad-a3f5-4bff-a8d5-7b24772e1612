﻿using Dexin.Service.DTOs.ChatView;
using Dexin.Service.Interface;
using Google.Cloud.Firestore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Client;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ChatController : ControllerBase
    {
        private readonly FirestoreDb _firestoreDb;
        private readonly IRoomService _roomService;
        private readonly ISystemUserAccountService _userAccountService;
        private readonly IClaimsService _claimsService;

        public ChatController(FirestoreDb firestoreDb, IRoomService roomService, ISystemUserAccountService userAccountService, IClaimsService claimsService)
        {
            _roomService = roomService;
            _firestoreDb = firestoreDb;
            _userAccountService = userAccountService;
            _claimsService = claimsService;
        }


        //[Authorize(Roles = "Admin")]

        [HttpGet("get-all-messages")]
        [Authorize(Roles = "Manager")]
        public async Task<IActionResult> GetAllMessages([FromQuery] int pageSize = 100, [FromQuery] string? pageToken = null)
        {
            Query query = _firestoreDb.CollectionGroup("Messages")
                                    .OrderByDescending("CreatedAt")
                                    .Limit(pageSize);

            if (!string.IsNullOrEmpty(pageToken))
            {
                if (long.TryParse(pageToken, out long unixTimeSeconds))
                {
                    var lastKnownDateTime = DateTimeOffset.FromUnixTimeSeconds(unixTimeSeconds).UtcDateTime;
                    var lastKnownTimestamp = Google.Cloud.Firestore.Timestamp.FromDateTime(lastKnownDateTime);
                    query = query.StartAfter(lastKnownTimestamp);
                }
            }

            QuerySnapshot snapshot = await query.GetSnapshotAsync();

            var messages = new List<Dictionary<string, object>>();
            long? nextPageTokenValue = null;

            foreach (var document in snapshot.Documents)
            {
                var messageData = document.ToDictionary();
                messageData["MessageId"] = document.Id;
                messageData["Path"] = document.Reference.Path;
                if (messageData.TryGetValue("CreatedAt", out object createdAtObj) && createdAtObj is Google.Cloud.Firestore.Timestamp timestamp)
                {
                    DateTime vietnamTime = timestamp.ToDateTime().AddHours(7);
                    messageData["CreatedAt"] = vietnamTime.ToString("M/d/yyyy h:mm:ss tt");
                }
                messages.Add(messageData);
            }
            if (snapshot.Documents.Count > 0)
            {
                var lastDoc = snapshot.Documents.Last();

                if (lastDoc.TryGetValue<Google.Cloud.Firestore.Timestamp>("CreatedAt", out var timestampValue))
                {
                    DateTime utcDateTime = timestampValue.ToDateTime();
                    DateTimeOffset offset = new DateTimeOffset(utcDateTime);
                    nextPageTokenValue = offset.ToUnixTimeSeconds();
                }
            }

            return Ok(new
            {
                Messages = messages,
                NextPageToken = nextPageTokenValue?.ToString()
            });
        }

        [HttpPost("create-room")]
        public async Task<IActionResult> CreateRoom([FromBody] CreateRoomRequest request)
        {
            if (request == null || string.IsNullOrEmpty(request.Name) || request.UserId == null || request.StaffId == null)
            {
                return BadRequest("Invalid request data.");
            }

            var roomCollection = _firestoreDb.Collection("ChatRooms");

            var user = await _userAccountService.GetUserProfileByIdAsync(request.UserId);
            var staff = await _userAccountService.GetUserProfileByIdAsync(request.StaffId);
            if (user.Data != null && staff.Data != null)
            {

                var mapUser = new Dictionary<string, object>()
                {
                    {"AccountId", user.Data.UserAccountId},
                    {"Name", user.Data.FirstName + " " + user.Data.LastName},
                    {"Avartar", user.Data.Avartar }
                };

                var mapStaff = new Dictionary<string, object>()
                {
                    {"AccountId", staff.Data.UserAccountId},
                    {"Name", staff.Data.FirstName + " " + staff.Data.LastName},
                    {"Avartar", staff.Data.Avartar }
                };


                var newRoom = new Dictionary<string, object>
                {
                    { "Name", request.Name },
                    { "CreatedAt", Timestamp.GetCurrentTimestamp() },
                    { "User", mapUser },
                    { "Staff", mapStaff }
                };

                // Firestore tự động tạo ID cho document
                DocumentReference addedDocRef = await roomCollection.AddAsync(newRoom);
                var model = new CreateRoomDTO
                {
                    RoomId = addedDocRef.Id.ToString(),
                    UserId = request.UserId,
                    StaffId = request.StaffId,

                };
                await _roomService.CreateRoomAsync(model);

                return Ok(new { RoomId = addedDocRef.Id });
            }
            return StatusCode(500, new { message = "Error with userId or staffId" });
        }

        [HttpGet("get-all-room")]
        public async Task<IActionResult> GetRooms()
        {
            var roomCollection = _firestoreDb.Collection("ChatRooms");
            // Truy vấn tất cả các phòng mà có userId nằm trong mảng ParticipantIds

            QuerySnapshot snapshot = await roomCollection.GetSnapshotAsync();

            var rooms = snapshot.Documents.Select(doc =>
            {
                var roomData = doc.ToDictionary();
                if (roomData.TryGetValue("CreatedAt", out object createdAtObj) && createdAtObj is Google.Cloud.Firestore.Timestamp timestamp)
                {
                    DateTime vietnamTime = timestamp.ToDateTime().AddHours(7);
                    roomData["CreatedAt"] = vietnamTime.ToString("M/d/yyyy h:mm:ss tt");
                }
                roomData["RoomId"] = doc.Id;
                return roomData;
            }).ToList();    

            return Ok(rooms);
        }

        [HttpGet("get-room-by-customer-id/{userId}")]
        public async Task<IActionResult> GetUserRooms(int userId)
        {
            var roomCollection = _firestoreDb.Collection("ChatRooms");
            // Truy vấn tất cả các phòng mà có userId nằm trong mảng ParticipantIds
            Query query = roomCollection.WhereEqualTo("UserId", userId);

            QuerySnapshot snapshot = await query.GetSnapshotAsync();

            var rooms = snapshot.Documents.Select(doc =>
            {
                var roomData = doc.ToDictionary();
                if (roomData.TryGetValue("CreatedAt", out object createdAtObj) && createdAtObj is Google.Cloud.Firestore.Timestamp timestamp)
                {
                    DateTime vietnamTime = timestamp.ToDateTime().AddHours(7);
                    roomData["CreatedAt"] = vietnamTime.ToString("M/d/yyyy h:mm:ss tt");
                }
                roomData["RoomId"] = doc.Id;
                return roomData;
            }).ToList();


            return Ok(rooms);
        }

        [HttpGet("get-room-by-staff-id/{staffId}")]
        public async Task<IActionResult> GetStaffRooms(int staffId)
        {
            var roomCollection = _firestoreDb.Collection("ChatRooms");

            Query query = roomCollection.WhereArrayContains("Staff", staffId);
            QuerySnapshot snapshot = await query.GetSnapshotAsync();

            var rooms = snapshot.Documents.Select(doc =>
            {
                var roomData = doc.ToDictionary();
                if (roomData.TryGetValue("CreatedAt", out object createdAtObj) && createdAtObj is Google.Cloud.Firestore.Timestamp timestamp)
                {
                    DateTime vietnamTime = timestamp.ToDateTime().AddHours(7);
                    roomData["CreatedAt"] = vietnamTime.ToString("M/d/yyyy h:mm:ss tt");
                }
                roomData["RoomId"] = doc.Id;
                return roomData;
            }).ToList();

            return Ok(rooms);
        }


        [HttpPost("send-message/{roomId}")]
        [Authorize(Roles ="User, Staff")]
        public async Task<IActionResult> SendMessage(string roomId, [FromBody] SendMessageRequest request)
        {

            if (string.IsNullOrEmpty(request.Text))
            {
                return BadRequest("Message text cannot be empty.");
            }


            var messageCollection = _firestoreDb.Collection("ChatRooms")
                                              .Document(roomId)
                                              .Collection("Messages");

            var role = _claimsService.GetCurrentRole;
            var userId = _claimsService.GetCurrentUserId;
            var user = await _userAccountService.GetUserProfileByIdAsync(userId);

            var mapStaff = new Dictionary<string, object>()
                {
                    {"AccountId", userId},
                    {"Name", user.Data.FirstName + " " + user.Data.LastName},
                    {"Avartar", user.Data.Avartar }
                };

            var newMessage = new Dictionary<string, object>
            {
                { "Text", request.Text },
                { role, mapStaff },
                { "CreatedAt", Timestamp.GetCurrentTimestamp() }
            };

            await messageCollection.AddAsync(newMessage);


            return Ok(new { Data = newMessage, Status = "Message sent successfully" });
        }

        [HttpGet("get-message-by-room-id/{roomId}")]
        public async Task<IActionResult> GetMessages(string roomId, [FromQuery] int limit = 20)
        {
            var messageCollection = _firestoreDb.Collection("ChatRooms")
                                              .Document(roomId)
                                              .Collection("Messages");

            Query query = messageCollection.OrderByDescending("CreatedAt")
                                           .Limit(limit);

            QuerySnapshot snapshot = await query.GetSnapshotAsync();

            var messages = new List<Dictionary<string, object>>();
            foreach (var document in snapshot.Documents)
            {
                var messageData = document.ToDictionary();
                if (messageData.TryGetValue("CreatedAt", out object createdAtObj) && createdAtObj is Google.Cloud.Firestore.Timestamp timestamp)
                {
                    DateTime vietnamTime = timestamp.ToDateTime().AddHours(7);
                    messageData["CreatedAt"] = vietnamTime.ToString("M/d/yyyy h:mm:ss tt");
                }
                messageData["MessageId"] = document.Id;
                messages.Add(messageData);
            }


            return Ok(messages);
        }
    }
}
