﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class OrderDetail
{
    public int OrderDetailId { get; set; }

    public int? Quantity { get; set; }

    public decimal? UnitPrice { get; set; }

    public decimal? TotalPrice { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? OrdersId { get; set; }

    public int? ProductId { get; set; }

    public int? ProductColorId { get; set; }

    public virtual Order? Orders { get; set; }

    public virtual Product? Product { get; set; }

    public virtual ProductColor? ProductColor { get; set; }

    public virtual ICollection<Review> Reviews { get; set; } = new List<Review>();
}
