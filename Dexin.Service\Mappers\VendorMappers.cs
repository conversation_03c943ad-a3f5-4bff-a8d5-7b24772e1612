﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.VendorView;

namespace Dexin.Service.Mappers
{
    public static class VendorMappers
    {
        public static VendorDTO ToVendorDTO(this Vendor vendor)
        {
            return new VendorDTO
            {
                Address = vendor.Address,
                CompanyName = vendor.CompanyName,
                ContactEmail = vendor.ContactEmail,
                CreatedAt = vendor.CreatedAt,
                Description = vendor.Description,
                LogoUrl = vendor.LogoUrl,
                ModifiedAt = vendor.ModifiedAt,
                Status = vendor.Status,
                VendorId = vendor.VendorId,
                TaxId = vendor.TaxId,
                UserAccountId = vendor.UserAccountId,
                UserAccount = vendor.UserAccount.ToUserProfileDTO(),
            };
        }
        public static Vendor FromCreateToVendor(this CreateVendorDTO vendor)
        {
            return new Vendor
            {
                Address = vendor.Address,
                CompanyName = vendor.CompanyName,
                ContactEmail = vendor.ContactEmail,
                Description = vendor.Description,
                LogoUrl = vendor.LogoUrl,
                Status = vendor.Status,
                TaxId = vendor.TaxId,
                UserAccountId = vendor.UserAccountId
            };
        }

        public static Vendor FromUpdateToVendor(this UpdateVendorDTO vendor)
        {
            return new Vendor
            {
                VendorId = vendor.VendorId,
                Address = vendor.Address,
                CompanyName = vendor.CompanyName,
                ContactEmail = vendor.ContactEmail,
                Description = vendor.Description,
                LogoUrl = vendor.LogoUrl,
                Status = vendor.Status,
                TaxId = vendor.TaxId,
                UserAccountId = vendor.UserAccountId
            };
        }
    }
}
