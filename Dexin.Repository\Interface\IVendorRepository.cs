﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IVendorRepository : IGenericRepository<Vendor>
    {
        Task<IEnumerable<Vendor>> GetAllIncludeAsync();
        Task<Vendor> GetByIdIncludeAsync(int id);
        Task<bool> CheckIdExisted(int id);
        Task<bool> CheckCompanyNameExisted(string companyName);
        Task<bool> CheckEmailExisted(string email);
        Task<bool> CheckTaxExisted(string tax); 
    }
}
