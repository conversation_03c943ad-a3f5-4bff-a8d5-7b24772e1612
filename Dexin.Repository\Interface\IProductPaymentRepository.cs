using Dexin.Repository.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Repository.Interface
{
    public interface IProductPaymentRepository : IGenericRepository<ProductPayment>
    {
        Task<IEnumerable<ProductPayment>> GetAllIncludeAsync();
        Task<ProductPayment> GetByIdIncludeAsync(int id);
        Task<bool> CheckIdExisted(int id);
        Task<IEnumerable<ProductPayment>> GetPaymentsByUserIdAsync(int userId);
        Task<IEnumerable<ProductPayment>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetTotalRevenueAsync();
        Task<decimal> GetRevenueByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<int> GetTotalPaymentsCountAsync();
        Task<int> GetPaymentCountByStatusAsync(string status);
        Task<IEnumerable<ProductPayment>> GetRecentPaymentsAsync(int limit);
        Task<int> GetPaymentCountByDateRangeAsync(DateTime startDate, DateTime endDate);
    }
}
