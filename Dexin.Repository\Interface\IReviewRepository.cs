using Dexin.Repository.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Repository.Interface
{
    public interface IReviewRepository : IGenericRepository<Review>
    {
        Task<IEnumerable<Review>> GetAllIncludeAsync();
        Task<Review> GetByIdIncludeAsync(int id);
        Task<bool> CheckIdExisted(int id);
        Task<IEnumerable<Review>> GetReviewsByProductIdAsync(int productId);
        Task<IEnumerable<Review>> GetReviewsByUserIdAsync(int userId);
        Task<double> GetAverageRatingAsync();
        Task<double> GetAverageRatingByProductIdAsync(int productId);
        Task<int> GetTotalReviewsCountAsync();
        Task<IEnumerable<Review>> GetRecentReviewsAsync(int limit);
    }
}
