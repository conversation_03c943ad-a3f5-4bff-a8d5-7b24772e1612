﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dexin.Service.DTOs.BlogTagView;
using Dexin.Service.Interface;
using Dexin.Service.APIResponse;
using Microsoft.AspNetCore.Authorization;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlogTagsController : ControllerBase
    {
        private readonly IBlogTagService _blogTagService;

        public BlogTagsController(IBlogTagService blogTagService)
        {
            _blogTagService = blogTagService;
        }

        // GET: api/BlogTags
        [HttpGet]
        public async Task<ActionResult<ResponseModels<IEnumerable<BlogTagDTO>>>> GetAllAsync()
        {
            var response = await _blogTagService.GetAllAsync();
            return Ok(response);
        }

        // GET: api/BlogTags/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ResponseModels<BlogTagDTO>>> GetByIdAsync(int id)
        {
            var response = await _blogTagService.GetByIdAsync(id);
            if (!response.Success)
            {
                return NotFound(response);
            }
            return Ok(response);
        }

        [HttpGet("name/{name}")]
        public async Task<ActionResult<ResponseModels<IEnumerable<BlogTagDTO>>>> GetByNameAsync(string name)
        {
            var response = await _blogTagService.SearchByNameAsync(name);
            if (!response.Success)
            {
                return NotFound(response);
            }
            return Ok(response);
        }

        // POST: api/BlogTags
        [HttpPost]
        [Authorize]
        public async Task<ActionResult<ResponseModels<CreateBlogTagDTO>>> CreateAsync(CreateBlogTagDTO blogTagDTO)
        {
            var response = await _blogTagService.CreateAsync(blogTagDTO);
            if (!response.Success)
            {
                return BadRequest(response);
            }
            return Ok(response);
        }

        // PUT: api/BlogTags/5
        [HttpPut("{id}")]
        [Authorize]
        public async Task<ActionResult<ResponseModels<UpdateBlogTagDTO>>> UpdateAsync(int id, UpdateBlogTagDTO blogTagDTO)
        {
            var response = await _blogTagService.UpdateByBlogTagAsync(id, blogTagDTO);
            if (!response.Success)
            {
                return BadRequest(response);
            }
            return Ok(response);
        }

        // DELETE: api/BlogTags/5
        [HttpDelete("{id}")]
        [Authorize]
        public async Task<ActionResult<ResponseModels<bool>>> DeleteAsync(int id)
        {
            var response = await _blogTagService.DeleteAsync(id);
            if (!response.Success)
            {
                return NotFound(response);
            }
            return Ok(response);
        }
    }
}
