﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.ItemCategoryView;

namespace Dexin.Service.Interface
{
    public interface IItemCategoryService
    {
        Task<ResponseModels<IEnumerable<ItemCategoryDTO>>> GetAllAsync();
        Task<ResponseModels<ItemCategoryDTO>> GetByIdAsync(int id);

        Task<ResponseModels<UpdateItemCategoryDTO>> UpdateAsync(int id, UpdateItemCategoryDTO item);
        Task<ResponseModels<bool>> DeleteAsync(int id);

        Task<ResponseModels<ItemCategoryDTO>> CreateAsync(CreateItemCategoryDTO item);
    }
}
