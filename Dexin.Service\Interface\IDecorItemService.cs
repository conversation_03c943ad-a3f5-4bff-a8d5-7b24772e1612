﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DecorItemView;

namespace Dexin.Service.Interface
{
    public interface IDecorItemService
    {
        Task<ResponseModels<IEnumerable<DecorItemDTO>>> GetAllAsync();
        Task<ResponseModels<DecorItemDTO>> GetByIdAsync(int id);
        Task<ResponseModels<UpdateDecorItemDTO>> UpdateAsync(int id, UpdateDecorItemDTO updateDecorItemDTO);
        Task<ResponseModels<bool>> DeleteAsync(int id);
        Task<ResponseModels<CreateDecorItemDTO>> CreateAsync(CreateDecorItemDTO createDecorItemDTO);

    }
}
