﻿
using Dexin.Repository.Enums;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.DesignView;
using Dexin.Service.Utils;

namespace Dexin.Service.Mappers
{
    public static class DesignMapper
    {
        public static DesignDTO ToDesignDTO(this Design model)
        {
            return new DesignDTO
            {
                CanvasPosX = model.CanvasPosX,
                CanvasPosY = model.CanvasPosY,
                CanvasScale = model.CanvasScale,
                CreatedAt = model.CreatedAt,
                CreatedBy = model.CreatedBy,
                Customer = model.CreatedByNavigation.ToUserProfileDTO(),
                DesignId = model.DesignId,
                DesignPayments = model.DesignPayments.ToList(),
                DesignDetails = model.DesignDetails.Select(d => d.ToDesignDetailDTO()).ToList(),
                ModifiedAt = model.ModifiedAt,
                Note = model.Note,
                Price = model.Price,
                PathUrl = model.PathUrl,
                Staff = model.Staff.ToUserProfileDTO(),
              //  StaffId = model.StaffId,
                Status = model.Status.ToVietNameseStatus(),
                ThumbnailUrl = model.ThumbnailUrl,
                Type = model.Type,
                Title = model.Title,
            };
        }

        public static Design FromCreateToDesign(this CreateDesignDTO model)
        {
            return new Design()
            {
                CanvasPosX = model.CanvasPosX,
                CanvasPosY = model.CanvasPosY,
                CanvasScale = model.CanvasScale,
                CreatedAt = DateTime.Now,
                CreatedBy = model.CustomerId,
                Note = model.Note,
                PathUrl = model.PathUrl,
                Price = model.Price,
               // StaffId = model.StaffId,
                Status = model.Status,
                ThumbnailUrl = model.ThumbnailUrl,
                Type = model.Type,
                Title = model.Title,

            };
        }

        public static Design FromUpdateToDesign(this UpdateDesignDTO model)
        {
            return new Design()
            {
                CanvasPosX = model.CanvasPosX,
                CanvasPosY = model.CanvasPosY,
                CanvasScale = model.CanvasScale,
                ModifiedAt = DateTime.Now,
                Note = model.Note,
                PathUrl = model.PathUrl,
                Price = model.Price,
                Status = model.Status,
                ThumbnailUrl = model.ThumbnailUrl,
                Type = model.Type,
                Title = model.Title,
            };
        }

        public static Design FromCreate3DToDesign(this Create3DDTO model)
        {
            return new Design()
            {
                CreatedAt = DateTime.Now,
                CreatedBy = model.CreatedBy,
                Note = model.Note,
                PathUrl = model.PathUrl,
                Price = model.Price,
              //  StaffId = model.StaffId,
                Status = (int) ThreeDStatus.Unpaid,
                ThumbnailUrl = model.ThumbnailUrl,
                Type = true,
                Title = model.Title,

            };
        }

    }
}
