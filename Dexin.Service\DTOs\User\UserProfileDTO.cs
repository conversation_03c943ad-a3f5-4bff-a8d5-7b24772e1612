﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CloudinaryDotNet.Actions;
using Dexin.Repository.Models;

namespace Dexin.Service.DTOs.User
{
    public class UserProfileDTO
    {
        public int UserAccountId { get; set; }

        public string UserName { get; set; } = null!;

        public string FirstName { get; set; } = null!;

        public string LastName { get; set; } = null!;

        public string Email { get; set; } = null!;
        public string? Role { get; set; }

        public string Phone { get; set; } = null!;
        public bool? Gender { get; set; }
        public bool IsActive { get; set; }

        public string? Avartar { get; set; }


    }
}
