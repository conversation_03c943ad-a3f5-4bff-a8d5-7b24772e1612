﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CloudinaryDotNet.Actions;
using CloudinaryDotNet;
using Microsoft.AspNetCore.Http;

namespace Dexin.Service.Interface
{
    public interface IImageService
    {
        Task<string> UploadImageAsync(IFormFile file);
        Task<List<string>> UploadImagesAsync(List<IFormFile> files); // New method

        Task<string> UploadObjFileAsync(IFormFile file);
        Task<string> UploadLargeFileAsync(IFormFile file);
        
        
    }
}
