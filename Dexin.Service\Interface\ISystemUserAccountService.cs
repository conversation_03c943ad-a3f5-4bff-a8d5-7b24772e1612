﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.User;

namespace Dexin.Service.Interface
{
    public interface ISystemUserAccountService
    {
        Task<ResponseModels<string>> LoginAsync(LoginDTO user);
        Task<ResponseModels<RegisterDTO>> RegisterAsync(RegisterDTO registerDTO);

        Task<ResponseModels<UserProfileDTO>> GetUserProfileByIdAsync(int id);
        Task<ResponseModels<UserProfileDTO>> CreateUserAsync(CreateUserDTO userDTO);
        Task<ResponseModels<bool>> UpdateUserImageAsync(int id, string avatarImage);
        Task<ResponseModels<UserProfileDTO>> UpdateUserProfileAsync(UpdateUserProfileDTO profileDTO, int id);
        Task<ResponseModels<UserProfileDTO>> UpdateAccountAsync(UpdateAccountDTO accountDTO, int id);
        Task<ResponseModels<IEnumerable<UserProfileDTO>>> GetAllCustomerAsync();
        Task<ResponseModels<IEnumerable<StaffProfileDTO>>> GetAllStaffProfilesAsync();  

        Task<ResponseModels<bool>> DeleteAsync(int id);
        Task<ResponseModels<string>> LoginManagerAccountAsync(LoginDTO accountObject);



    }
}
