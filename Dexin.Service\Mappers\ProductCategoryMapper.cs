﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon.Runtime.Internal.Util;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.ProductCategoryView;
using Grpc.Core;

namespace Dexin.Service.Mappers
{
    public static class ProductCategoryMapper
    {
        public static ProductCategoryDTO ToProductCategoryDTO(this ProductCategory productCategory)
        {
            return new ProductCategoryDTO
            {
                CreatedAt = productCategory.CreatedAt,
                Description = productCategory.Description,
                ModifiedAt = productCategory.ModifiedAt,
                Name = productCategory.Name,
                ParentId = productCategory.ParentId,
                ProductCategoryId = productCategory.ProductCategoryId,
                Products = productCategory.Products,
                Status = productCategory.Status,
                UserAccountId = productCategory.UserAccountId,
            };
        }

        public static ProductCategory FromCreateToProductCategory(this CreateProductCategoryDTO createProductCategoryDTO)
        {
            return new ProductCategory
            {
                Name = createProductCategoryDTO.Name,
                Status = createProductCategoryDTO.Status,
                Description = createProductCategoryDTO.Description,

            };
        }

        public static ProductCategory FromUpdateToProductCategory(this UpdateProductCategoryDTO updateProductCategoryDTO)
        {
            return new ProductCategory
            {
                Name = updateProductCategoryDTO.Name,
                Status = updateProductCategoryDTO.Status,
                Description = updateProductCategoryDTO.Description,
                ProductCategoryId = updateProductCategoryDTO.ProductCategoryId,
                UserAccountId = updateProductCategoryDTO.UserAccountId
            };
        }
    }
}
