﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.ChatView;

namespace Dexin.Service.Interface
{
    public interface IRoomService
    {
        Task<ResponseModels<IEnumerable<RoomDTO>>> GetAllIncludeAsyn();
        Task<ResponseModels<string>> CreateRoomAsync(CreateRoomDTO roomDTO);
        Task<ResponseModels<IEnumerable<RoomDTO>>> GetAllByCustomerIdAsync(int id);
        Task<ResponseModels<IEnumerable<RoomDTO>>> GetAllByStaffId(int staffId);
        Task<ResponseModels<RoomDTO>> GetByRoomIdAsync(string roomId);
    }
}
