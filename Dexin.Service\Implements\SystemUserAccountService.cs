﻿using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.Commons;
using Dexin.Service.DTOs.User;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Dexin.Service.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class SystemUserAccountService : ISystemUserAccountService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AppConfiguration _configuration;
        private readonly ICurrentTime _currentTime;
        private readonly IClaimsService _claimsService;
        private readonly ILogger<SystemUserAccount> _logger;
        public SystemUserAccountService(IUnitOfWork unitOfWork, ICurrentTime currentTime, AppConfiguration configuration, ILogger<SystemUserAccount> logger, IClaimsService claimsService)
        {
            _unitOfWork = unitOfWork;
            _currentTime = currentTime;
            _configuration = configuration;
            _logger = logger;
            _claimsService = claimsService;
        }

        public async Task<ResponseModels<string>> LoginManagerAccountAsync(LoginDTO accountObject)
        {
            var response = new ResponseModels<string>()
            {
                Data = null,
                Success = false
            };
            var adminAccount = _configuration.AdminAccount;
            if (accountObject.UserName == adminAccount.UserName && accountObject.Password == adminAccount.Password)
            {
                response.Data = adminAccount.GenerateJsonWebToken(_configuration.JWTSection, _currentTime.GetCurrentTime());
                response.Success = true;
                response.Message = "Login success !";
                return response;
            }
            response.Message = "Password is incorrect ! Please try again !";
            return response;
        }

        public async Task<ResponseModels<UserProfileDTO>> GetUserProfileByIdAsync(int id)
        {
            var response = new ResponseModels<UserProfileDTO>()
            {
                Data = null,
                Success = false

            };
            try
            {
                var user = await _unitOfWork.SystemUserAccountRepository.GetByIdAsync(id);
                if (user != null)
                {
                    var dto = user.ToUserProfileDTO();
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = dto;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Get Error");
                return response;
            }
        }
        public async Task<ResponseModels<string>> LoginAsync(LoginDTO user)
        {
            var response = new ResponseModels<string>()
            {
                Data = null,
                Success = false

            };

            var userAccount = await _unitOfWork.SystemUserAccountRepository.GetUserByEmailAsync(user.UserName);
            if (userAccount is null)
            {
                userAccount = await _unitOfWork.SystemUserAccountRepository.GetUserByUserNameAsync(user.UserName);
                if (userAccount is null)
                {
                    response.Message = "Login Failed!";
                    return response;
                }
            }

            if (user.Password.Hash() != userAccount.Password)
            {
                response.Message = "Login Failed!";
                return response;
            }

            var account = await _unitOfWork.SystemUserAccountRepository.GetByIdAsync(userAccount.UserAccountId);
            response.Data = account.GenerateJsonWebToken(_configuration.JWTSection, _currentTime.GetCurrentTime());
            response.Success = true;
            response.Message = "Đăng nhập thành công";
            return response;
        }

        public async Task<ResponseModels<RegisterDTO>> RegisterAsync(RegisterDTO registerDTO)
        {
            var response = new ResponseModels<RegisterDTO>()
            {
                Data = null,
                Success = false
            };

            if (await _unitOfWork.SystemUserAccountRepository.CheckEmailExisted(registerDTO.Email))
            {
                response.Message = "Email đã được đăng ký! Vui lòng sử dụng Email khác!";
                return response;
            }
            else if (await _unitOfWork.SystemUserAccountRepository.CheckUserNameExisted(registerDTO.Username))
            {
                response.Message = "Tên đăng nhập đã tồn tại! Vui lòng chọn tên đăng nhập khác!";
                return response;
            }

            registerDTO.Password = registerDTO.Password.Hash();
            SystemUserAccount account = registerDTO.ToUserAccount();

            var rs = await _unitOfWork.SystemUserAccountRepository.CreateAsync(account);
            if (rs > 0)
            {
                response.Success = true;
                response.Data = registerDTO;
                response.Message = "Đăng ký thành công!";
                return response;
            }
            else
            {
                response.Message = "Đã xảy ra lỗi! Vui lòng thủ lại!";
                return response;
            }
        }
        public async Task<ResponseModels<UserProfileDTO>> CreateUserAsync(CreateUserDTO userDTO)
        {
            var response = new ResponseModels<UserProfileDTO>()
            {
                Data = null,
                Success = false
            };

            if (await _unitOfWork.SystemUserAccountRepository.CheckEmailExisted(userDTO.Email))
            {
                response.Message = "Email đã được đăng ký! Vui lòng sử dụng Email khác!";
                return response;
            }
            else if (await _unitOfWork.SystemUserAccountRepository.CheckUserNameExisted(userDTO.UserName))
            {
                response.Message = "Tên đăng nhập đã tồn tại! Vui lòng chọn tên đăng nhập khác!";
                return response;
            }

            userDTO.Password = userDTO.Password.Hash();
            var account = userDTO.FromCreateToUser();
            account.CreatedBy = _claimsService.GetCurrentUserName;
            account.CreatedDate = _currentTime.GetCurrentTime();

            var rs = await _unitOfWork.SystemUserAccountRepository.CreateAsync(account);

            if (rs > 0)
            {
                response.Success = true;
                response.Data = account.ToUserProfileDTO();
                response.Message = "Đăng ký thành công!";
                return response;
            }
            else
            {
                response.Message = "Đã xảy ra lỗi! Vui lòng thủ lại!";
                return response;
            }
        }

        public async Task<ResponseModels<bool>> UpdateUserImageAsync(int userId, string avatarImage)
        {
            var response = new ResponseModels<bool>()
            {
                Data = false,
                Success = false
            };

            var user = await _unitOfWork.SystemUserAccountRepository.GetByIdAsync(userId);

            if (user == null)
            {
                response.Message = "Không tìm thấy người dùng!";
                return response;
            }


            user.Avartar = avatarImage;
            var rs = await _unitOfWork.SystemUserAccountRepository.UpdateAsync(user);

            if (rs > 0)
            {
                response.Success = true;
                response.Data = true;
                response.Message = "Cập nhật ảnh thành công!";
                return response;
            }
            else
            {
                response.Message = "Đã xảy ra lỗi! Vui lòng thủ lại!";
                return response;
            }
        }

        public async Task<ResponseModels<UserProfileDTO>> UpdateUserProfileAsync(UpdateUserProfileDTO profileDTO, int id)
        {
            var response = new ResponseModels<UserProfileDTO>
            {
                Success = false,
                Data = null,
            };

            try
            {
                if (!await _unitOfWork.SystemUserAccountRepository.CheckIdExisted(id))
                {
                    response.Message = "Không tìm thấy người dùng!";
                    return response;
                }

                var user = profileDTO.FromUpdateToUserAccount();
                var userExisted = await _unitOfWork.SystemUserAccountRepository.GetByIdAsync(id);

                user.Password = userExisted.Password;
                user.EmployeeCode = userExisted.EmployeeCode;
                user.CreatedBy = userExisted.CreatedBy;
                user.Avartar = userExisted.Avartar;

                user.UserAccountId = id;
                user.ModifiedBy = _claimsService.GetCurrentUserName;
                user.ModifiedDate = _currentTime.GetCurrentTime();

                var rs = await _unitOfWork.SystemUserAccountRepository.UpdateAsync(user);
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Cập nhật thành công!";
                    response.Data = user.ToUserProfileDTO();
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;

            }

            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Update Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<UserProfileDTO>> UpdateAccountAsync(UpdateAccountDTO accountDTO, int id)
        {
            var response = new ResponseModels<UserProfileDTO>
            {
                Success = false,
                Data = null,
            };

            try
            {
                if (!await _unitOfWork.SystemUserAccountRepository.CheckIdExisted(id))
                {
                    response.Message = "Không tìm thấy người dùng!";
                    return response;
                }

                var user = accountDTO.FromUpdateAccountToUserAccount();
                user.UserAccountId = id;
                user.ModifiedBy = _claimsService.GetCurrentUserName;
                user.ModifiedDate = _currentTime.GetCurrentTime();

                var rs = await _unitOfWork.SystemUserAccountRepository.UpdateAsync(user);
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Cập nhật thành công!";
                    response.Data = user.ToUserProfileDTO();
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;

            }

            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại";
                _logger.LogError(ex, "Update Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<UserProfileDTO>>> GetAllCustomerAsync()
        {
            var response = new ResponseModels<IEnumerable<UserProfileDTO>>
            {
                Success = false,
                Data = null,
            };

            try
            {
                var list = await _unitOfWork.SystemUserAccountRepository.GetAllCustomersAsync();


                if (list.Count() > 0)
                {
                    var customer = list.Select(a => a.ToUserProfileDTO()).ToList();

                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = customer;
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                    return response;
                }

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<StaffProfileDTO>>> GetAllStaffProfilesAsync()
        {
            var response = new ResponseModels<IEnumerable<StaffProfileDTO>>
            {
                Success = false,
                Data = null,
            };

            try
            {
                var list = await _unitOfWork.SystemUserAccountRepository.GetAllStaffAsync();


                if (list.Count() > 0)
                {
                    var staffs = list.Select(a => a.ToStaffDTO()).ToList();

                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = staffs;
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                    return response;
                }

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>
            {
                Success = false,
                Data = false,
            };

            try
            {
                if (await _unitOfWork.SystemUserAccountRepository.CheckIdExisted(id))
                {
                    var user = await _unitOfWork.SystemUserAccountRepository.GetByIdAsync(id);
                    var rs = await _unitOfWork.SystemUserAccountRepository.RemoveAsync(user);
                    if (rs)
                    {
                        response.Success = true;
                        response.Message = "Xóa thành công";
                        response.Data = rs;
                        return response;
                    }
                   
                }
               
                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (DbUpdateException dbEx)
            {
                response.Message = "Không thể xóa tài khoản vì có dữ liệu liên kết.";
                _logger.LogError(dbEx, "Foreign key constraint violation");
                return response;
            }
            catch (InvalidOperationException invEx)
            {
                response.Message = "Đã có lỗi xảy ra trong quá trình xử lý đối tượng.";
                _logger.LogError(invEx, "Invalid operation during delete");
                return response;
            }
        }
    }
}
