﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class ItemCategory
{
    public int ItemCategoryId { get; set; }

    public string? Name { get; set; }

    public string? Slug { get; set; }

    public string? Description { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public virtual ICollection<DecorItem> DecorItems { get; set; } = new List<DecorItem>();
}
