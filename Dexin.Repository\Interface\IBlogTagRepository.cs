﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore.SqlServer.Query.Internal;

namespace Dexin.Repository.Interface
{
    public interface IBlogTagRepository : IGenericRepository<BlogTag>
    {
        Task<bool> CheckNameExisted(string name);
        Task<IEnumerable<BlogTag>> GetAllIncludeAsync();
        Task<IEnumerable<BlogTag>> SearchByNameIncludeAsync(string name);
        Task<bool> CheckIdExisted (int id);
        Task<BlogTag> GetByIdIncludeAsync(int id);
    }
}
