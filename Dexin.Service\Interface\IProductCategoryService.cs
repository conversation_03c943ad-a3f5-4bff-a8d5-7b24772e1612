﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.ProductCategoryView;

namespace Dexin.Service.Interface
{
    public interface IProductCategoryService
    {
        Task<ResponseModels<IEnumerable<ProductCategoryDTO>>> GetAllAsync();
        Task<ResponseModels<ProductCategoryDTO>> GetByIdAsync(int id);
        Task<ResponseModels<CreateProductCategoryDTO>> CreateAsync(CreateProductCategoryDTO productCategoryDTO);
        Task<ResponseModels<UpdateProductCategoryDTO>> UpdateAsync(UpdateProductCategoryDTO productCategoryDTO);
        Task<ResponseModels<bool>> DeleteAsync(int id);
    }
}
