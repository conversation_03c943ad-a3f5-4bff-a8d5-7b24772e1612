﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.ChatView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class RoomService : IRoomService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IClaimsService _claimsService;
        private readonly ILogger<Room> _logger;
        private readonly ICurrentTime _currentTime;
        public RoomService(IUnitOfWork unitOfWork, IClaimsService claimService, ILogger<Room> logger, ICurrentTime currentTime)
        {
            _claimsService = claimService;
            _unitOfWork = unitOfWork;
            _currentTime = currentTime;
            _logger = logger;
        }
        public async Task<ResponseModels<string>> CreateRoomAsync(CreateRoomDTO roomDTO)
        {
            var response = new ResponseModels<string>()
            {

                Success = false,
                Data = null,
            };

            try
            {
                if (await _unitOfWork.RoomRepository.ChekIdExisted(roomDTO.RoomId))
                {
                   
                    response.Message = "RoomId đã tồn tại! Vui lòng thử lại";
                    return response;
                }

                var model = roomDTO.FromCreateToRoom();
                model.CreatedDate = _currentTime.GetCurrentTime();
                model.IsActive = true;
                

                var rs = await _unitOfWork.RoomRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Tạo phòng chat thành công!";
                    response.Data = roomDTO.RoomId;
                }

                  
            }
            catch (Exception ex){
                _logger.LogError(ex, "Create Error: ");
            }
            response.Message = "Đã xảy ra lỗi vui lòng thử lại";
            return response;
        }

        public async Task<ResponseModels<IEnumerable<RoomDTO>>> GetAllByCustomerIdAsync(int id)
        {
            var response = new ResponseModels<IEnumerable<RoomDTO>>()
            {

                Success = false,
                Data = null,
            };

            try
            {
              
                var rs = await _unitOfWork.RoomRepository.GetAllByCustomerIdIncludeAsync(id);
                if (rs.Count() > 0)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = rs.Select(r => r.ToRoomDTO()).ToList();
                }
                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                }

               
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get Error: ");
                response.Message = "Đã xảy ra lỗi vui lòng thử lại";
                return response;
            }

            return response;
            
        }

        public async Task<ResponseModels<IEnumerable<RoomDTO>>> GetAllByStaffId(int staffId)
        {
            var response = new ResponseModels<IEnumerable<RoomDTO>>()
            {

                Success = false,
                Data = null,
            };

            try
            {

                var rs = await _unitOfWork.RoomRepository.GetAllByStaffIdIncludeAsync(staffId);
                if (rs.Count() > 0)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = rs.Select(r => r.ToRoomDTO()).ToList();
                }
                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                }


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get Error: ");
                response.Message = "Đã xảy ra lỗi vui lòng thử lại";
                return response;
            }

            return response;
        }

        public async Task<ResponseModels<IEnumerable<RoomDTO>>> GetAllIncludeAsyn()
        {
            var response = new ResponseModels<IEnumerable<RoomDTO>>()
            {

                Success = false,
                Data = null,
            };

            try
            {

                var rs = await _unitOfWork.RoomRepository.GetAllIncludeAsync();
                if (rs.Count() > 0)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = rs.Select(r => r.ToRoomDTO()).ToList();
                }
                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                }


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get Error: ");
                response.Message = "Đã xảy ra lỗi vui lòng thử lại";
                return response;
            }

            return response;
        }

        public async Task<ResponseModels<RoomDTO>> GetByRoomIdAsync(string roomId)
        {
            var response = new ResponseModels<RoomDTO>()
            {

                Success = false,
                Data = null,
            };

            try
            {

                var rs = await _unitOfWork.RoomRepository.GetByIdIncludeAsync(roomId);
                if (rs != null)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = rs.ToRoomDTO();
                }
                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                }


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get Error: ");
                response.Message = "Đã xảy ra lỗi vui lòng thử lại";
                return response;
            }

            return response;
        }
    }
}
