﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.DTOs.DesignPaymentView
{
    public class CreateDesignPaymentDTO
    {

        public string? PaymentMethod { get; set; }

        public string? PaymentProvide { get; set; }

        public decimal? Amount { get; set; }

        public string? CurrencyCode { get; set; }

        public string? TransactionIdGateway { get; set; }

        public DateTime? PaymentDate { get; set; }

        public string? GatewayResponse { get; set; }

        public string? Status { get; set; }

        public int? DesignId { get; set; }

    }
}
