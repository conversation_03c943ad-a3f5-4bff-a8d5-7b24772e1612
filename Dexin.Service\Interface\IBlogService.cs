﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.BlogView;

namespace Dexin.Service.Interface
{
    public interface IBlogService
    {
        Task<ResponseModels<IEnumerable<BlogDTO>>> GetAllAync();
        Task<ResponseModels<CreateBlogDTO>> CreateAsync(Create<PERSON><PERSON><PERSON><PERSON> createBlogDTO, string user);

        Task<ResponseModels<UpdateBlogDTO>> UpdateAsync(int id, UpdateBlogDTO updateBlogDTO);

        Task<ResponseModels<bool>> DeleteAsync(int id);
        Task<ResponseModels<IEnumerable<BlogDTO>>> SearchAsync(string? title, string? subtitle, string? content);

    }
}
