﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Dexin.Repository.Implements
{
    public class BlogRepository : GenericRepository<Blog>, IBlogRepository
    {
        public BlogRepository()
        {

        }

        public BlogRepository(DexinContext context) => _context = context;
        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.Blogs.AnyAsync(b => b.BlogPostId == id);
        }

        //public async Task<int> CreateWithListTagsImagesAsync(Blog blog, List<int> tagIds, List<string> images)
        //{
        //    blog.BlogPostTags = tagIds.Select(tagId => new BlogPostTag
        //    {
        //        BlogTagId = tagId,
        //        BlogPostId = blog.BlogPostId,
        //    }).ToList();

        //    blog.BlogImages = images.Select(image => new BlogImage
        //    {
        //        BlogPostId = blog.BlogPostId,
        //        ImageUrl = image
        //    }).ToList();

        //    await _context.Blogs.AddAsync(blog);
        //    return await _context.SaveChangesAsync();
        //}

        //public async Task<int> UpdateWithListTagsImagesAsync(Blog blog, List<int> tagIds, List<string> images)
        //{
        //    blog.BlogPostTags = tagIds.Select(tagId => new BlogPostTag
        //    {
        //        BlogTagId = tagId,
        //        BlogPostId = blog.BlogPostId,
        //    }).ToList();

        //    blog.BlogImages = images.Select(image => new BlogImage
        //    {
        //        BlogPostId = blog.BlogPostId,
        //        ImageUrl = image
        //    }).ToList();


        //    _context.ChangeTracker.Clear();
        //    var tracker = _context.Attach(blog);
        //    tracker.State = EntityState.Modified;
        //    return await _context.SaveChangesAsync();
        //}

        public async Task<IEnumerable<Blog>> GetAllInCLudeAsync()
        {
            return await _context.Blogs
                        .Include(b => b.UserAccount)
                        .Include(b => b.BlogPostTags)
                        .Include(b => b.BlogImages)
                        .Include(b => b.BlogLikes)
                        .ToListAsync();

        }

        public async Task<Blog> GetByIdIncludeAsync(int id)
        {
            return await _context.Blogs
                      .Include(b => b.UserAccount)
                      .Include(b => b.BlogPostTags)
                      .Include(b => b.BlogImages)
                      .Include(b => b.BlogLikes)
                      .FirstOrDefaultAsync(b => b.BlogPostId == id) ?? new Blog();
        }

        public async Task<IEnumerable<Blog>> SearchIncludeAysnc(string title, string subtitle, string content)
        {
            var rs = await _context.Blogs
                        .Include(b => b.UserAccount)
                        .Include(b => b.BlogPostTags)
                        .Include(b => b.BlogImages)
                        .Include(b => b.BlogLikes)
                        .Where(b =>
                            (b.Title.Contains(title) || string.IsNullOrEmpty(title)) &&
                            (b.Subtitle.Contains(subtitle) || string.IsNullOrEmpty(subtitle)) &&
                            (b.Content.Contains(content) || string.IsNullOrEmpty(content)))
                        .ToListAsync();
            return rs;
        }

        // Dashboard methods
        public async Task<int> GetTotalBlogsCountAsync()
        {
            return await _context.Blogs.CountAsync();
        }
    }
}
