﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.DesignDetailView;

namespace Dexin.Service.Mappers
{
    public static class DesignDetailMapper
    {
        public static DesignDetailDTO ToDesignDetailDTO(this DesignDetail model)
        {
            return new DesignDetailDTO
            {
                CreatedAt = model.CreatedAt,
                ModifiedAt = model.ModifiedAt,
                DecorItemId = model.DecorItemId,
                DesignDetailId = model.DesignDetailId,
                DesignId = model.DesignId,
                Height = model.Height,
                Layer = model.Layer,
                PositionX = model.PositionX,
                PositionY = model.PositionY,
                Rotation = model.Rotation,
                Width = model.Width,
                Status = model.Status,
                DecorItem = model.DecorItem.ToDecorItemDTO()
            };
        }

        public static DesignDetail FromCreateToDesignDetail(this CreateDesignDetailDTO model)
        {
            return new DesignDetail
            {
                DecorItemId = model.DecorItemId,
                CreatedAt = DateTime.Now,
                //DesignId = model.DesignId,
                Height = model.Height,
                Layer = model.Layer,
                PositionX = model.PositionX,
                PositionY = model.PositionY,
                Rotation = model.Rotation,
                Width = model.Width,
                Status = model.Status,
            };
        }

        public static DesignDetail FromUpdateToDesignDetailDTO(this UpdateDesignDetailDTO model)
        {
            return new DesignDetail
            {
                DesignDetailId = model.DesignDetailId ?? 0,
                Rotation = model.Rotation,
                PositionX = model?.PositionX,
                PositionY = model?.PositionY,
                Layer = model?.Layer,
                Height = model?.Height,
                DecorItemId = model?.DecorItemId,
                //DesignId = model?.DesignId,
                Status = model?.Status,
                Width = model?.Width,
                ModifiedAt = DateTime.Now,
            };
        }
    }
}