﻿using Dexin.Service.DTOs.ImageModel;
using Dexin.Service.Implements;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UploadsController : ControllerBase
    {
        private readonly S3StorageService _storageService;

        public UploadsController(S3StorageService storageService)
        {
            _storageService = storageService;
        }

        [HttpPost("obj")]
        [RequestSizeLimit(long.MaxValue)]
        [DisableRequestSizeLimit]
        public async Task<IActionResult> UploadObj([FromForm] ImageUploadRequestModel model)
        {
            if (model?.File == null || model.File.Length == 0)
                return BadRequest("No file uploaded.");

            var url = await _storageService.UploadFileAsync(model.File);

            return Ok(new { Url = url });
        }
    }
}
