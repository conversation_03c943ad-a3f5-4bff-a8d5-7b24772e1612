﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class CommunityPost
{
    public int CommunityPostId { get; set; }

    public string? Title { get; set; }

    public string? Content { get; set; }

    public int? Likes { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? UserAccountId { get; set; }

    public int? Views { get; set; }

    public virtual ICollection<CommunityComment> CommunityComments { get; set; } = new List<CommunityComment>();

    public virtual ICollection<CommunityLike> CommunityLikes { get; set; } = new List<CommunityLike>();

    public virtual ICollection<CommunityPostTag> CommunityPostTags { get; set; } = new List<CommunityPostTag>();

    public virtual SystemUserAccount? UserAccount { get; set; }
}
