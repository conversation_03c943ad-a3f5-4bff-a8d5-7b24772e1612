using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.DTOs.DashboardView
{
    public class TransactionStatisticsDTO
    {
        public RevenueStatisticsDTO Revenue { get; set; } = new RevenueStatisticsDTO();
        public PaymentStatisticsDTO DesignPayments { get; set; } = new PaymentStatisticsDTO();
        public PaymentStatisticsDTO ProductPayments { get; set; } = new PaymentStatisticsDTO();
        public PaymentStatusStatisticsDTO PaymentStatus { get; set; } = new PaymentStatusStatisticsDTO();
        public List<RevenueDataDTO> RevenueData { get; set; } = new List<RevenueDataDTO>();
    }

    public class RevenueStatisticsDTO
    {
        public decimal TotalRevenue { get; set; }
        public decimal RevenueThisMonth { get; set; }
        public decimal RevenueThisWeek { get; set; }
        public decimal RevenueToday { get; set; }
        public decimal DesignRevenue { get; set; }
        public decimal ProductRevenue { get; set; }
    }

    public class PaymentStatisticsDTO
    {
        public int TotalPayments { get; set; }
        public int PaymentsThisMonth { get; set; }
        public int PaymentsThisWeek { get; set; }
        public int PaymentsToday { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class PaymentStatusStatisticsDTO
    {
        public int SuccessfulPayments { get; set; }
        public int PendingPayments { get; set; }
        public int FailedPayments { get; set; }
        public int CancelledPayments { get; set; }
    }

    public class RevenueDataDTO
    {
        public DateTime Date { get; set; }
        public decimal DesignRevenue { get; set; }
        public decimal ProductRevenue { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TransactionCount { get; set; }
    }
}
