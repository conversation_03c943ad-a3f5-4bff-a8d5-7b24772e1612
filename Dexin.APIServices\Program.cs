using System.Text;
using System.Text.Json.Serialization;
using Dexin.APIServices;
using Dexin.APIServices.Hubs;
using Dexin.Repository.DBContext;
using Dexin.Service.Commons;
using Dexin.Service.Implements;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.Firestore.V1;
using Google.Cloud.Firestore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Dexin.Repository.Enums;
using Dexin.Service.Interface;
using Net.payOS;

var builder = WebApplication.CreateBuilder(args);

// Bind AppConfiguration from appsettings.json
builder.Services.Configure<AppConfiguration>(builder.Configuration);
builder.Services.Configure<PayOSConfiguration>(builder.Configuration.GetSection("PayOS"));
builder.Services.Configure<JWTSection>(builder.Configuration.GetSection("JWTSection"));
builder.Services.Configure<CloudinarySetting>(builder.Configuration.GetSection("CloudinarySetting"));


// Register DbContext
builder.Services.AddDbContext<DexinContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// CORS policy
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin", policy =>
        policy
              .AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod());
              //.AllowCredentials());
});


builder.Services.AddWebAPIService(builder.Configuration);
builder.Services.AddHttpContextAccessor();
builder.Services.AddLogging();
builder.Services.AddSignalR();

var appConfig = builder.Configuration.Get<AppConfiguration>();
builder.Services.AddSingleton(appConfig);

var payOsConfiguration = builder.Configuration.GetSection("PayOS");
builder.Services.AddSingleton(new PayOS(
    payOsConfiguration["ClientId"] ?? throw new ArgumentNullException("PayOS:ClientId"),
    payOsConfiguration["ApiKey"] ?? throw new ArgumentNullException("PayOS:ApiKey"),
    payOsConfiguration["ChecksumKey"] ?? throw new ArgumentNullException("PayOS:ChecksumKey")
));

builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.Never;
}); ;

builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = 2L * 1024 * 1024 * 1024; // 2GB
});



var projectId = builder.Configuration["Firebase:ProjectId"];
var credentialPath = builder.Configuration["Firebase:CredentialPath"];

Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", credentialPath);

FirebaseApp.Create(new AppOptions()
{
    Credential = GoogleCredential.GetApplicationDefault(),
});

builder.Services.AddSingleton(provider =>
{
    var projectId = builder.Configuration["Firebase:ProjectId"];
    return FirestoreDb.Create(projectId);
});

// Configure JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = appConfig.JWTSection.Issuer,
            ValidAudience = appConfig.JWTSection.Audience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(appConfig.JWTSection.SecretKey)),
            RoleClaimType = "Role"
        };
    });


builder.Services.AddSingleton<S3StorageService>();

// Add Swagger with JWT support
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo { Title = "Dexin API", Version = "v1" });
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        In = ParameterLocation.Header,
        Description = "Enter JWT token as: Bearer {your token}",
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    //options.OperationFilter<SwaggerFileOperationFilter>();
});

builder.Services.AddDistributedMemoryCache();
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("Manager", policy => policy.RequireRole(Role.Manager.ToString()));
    options.AddPolicy("Staff", policy => policy.RequireRole(Role.Staff.ToString()));
    options.AddPolicy("Vendor", policy => policy.RequireRole(Role.Vendor.ToString()));
    options.AddPolicy("User", policy => policy.RequireRole(Role.User.ToString()));
});

var app = builder.Build();


app.UseSwagger();
app.UseSwaggerUI();


app.UseHttpsRedirection();

app.UseCors("AllowSpecificOrigin");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.MapHub<ChatHub>("/chatHub");

app.Run();
