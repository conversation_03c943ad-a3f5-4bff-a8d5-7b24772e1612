﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class ProductPayment
{
    public int ProductPaymentId { get; set; }

    public string? PaymentMethod { get; set; }

    public string? PaymentProvide { get; set; }

    public decimal? Amount { get; set; }

    public string? CurrencyCode { get; set; }

    public string? TransactionIdGateway { get; set; }

    public DateTime? PaymentDate { get; set; }

    public string? GatewayResponse { get; set; }

    public string? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? OrdersId { get; set; }

    public int? UserAccountId { get; set; }

    public virtual Order? Orders { get; set; }

    public virtual SystemUserAccount? UserAccount { get; set; }
}
