﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class BlogImageRepository : GenericRepository<BlogImage>, IBlogImageRepository
    {
        public async Task<bool> DeleteListAsync(List<int> ids)
        {
            var images = await _context.BlogImages
                                       .Where(img => ids.Contains(img.ImageId))
                                       .ToListAsync();

            if (images == null || !images.Any())
                return false;

            _context.BlogImages.RemoveRange(images);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
