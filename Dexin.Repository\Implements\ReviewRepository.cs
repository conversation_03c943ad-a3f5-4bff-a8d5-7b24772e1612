using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Repository.Implements
{
    public class ReviewRepository : GenericRepository<Review>, IReviewRepository
    {
        public ReviewRepository(DexinContext context) => _context = context;

        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.Reviews.AnyAsync(r => r.ReviewId == id);
        }

        public async Task<IEnumerable<Review>> GetAllIncludeAsync()
        {
            return await _context.Reviews
                .Include(r => r.UserAccount)
                .Include(r => r.Product)
                .Include(r => r.OrderDetail)
                .ToListAsync();
        }

        public async Task<double> GetAverageRatingAsync()
        {
            var reviews = await _context.Reviews
                .Where(r => r.Rating.HasValue)
                .ToListAsync();

            if (!reviews.Any())
                return 0;

            return reviews.Average(r => r.Rating.Value);
        }

        public async Task<double> GetAverageRatingByProductIdAsync(int productId)
        {
            var reviews = await _context.Reviews
                .Where(r => r.ProductId == productId && r.Rating.HasValue)
                .ToListAsync();

            if (!reviews.Any())
                return 0;

            return reviews.Average(r => r.Rating.Value);
        }

        public async Task<Review> GetByIdIncludeAsync(int id)
        {
            return await _context.Reviews
                .Include(r => r.UserAccount)
                .Include(r => r.Product)
                .Include(r => r.OrderDetail)
                .FirstOrDefaultAsync(r => r.ReviewId == id) ?? new Review();
        }

        public async Task<IEnumerable<Review>> GetRecentReviewsAsync(int limit)
        {
            return await _context.Reviews
                .Include(r => r.UserAccount)
                .Include(r => r.Product)
                .OrderByDescending(r => r.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<IEnumerable<Review>> GetReviewsByProductIdAsync(int productId)
        {
            return await _context.Reviews
                .Where(r => r.ProductId == productId)
                .Include(r => r.UserAccount)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Review>> GetReviewsByUserIdAsync(int userId)
        {
            return await _context.Reviews
                .Where(r => r.UserAccountId == userId)
                .Include(r => r.Product)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        public async Task<int> GetTotalReviewsCountAsync()
        {
            return await _context.Reviews.CountAsync();
        }
    }
}
