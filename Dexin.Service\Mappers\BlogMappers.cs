﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.BlogView;

namespace Dexin.Service.Mappers
{
    public static class BlogMappers
    {
        public static BlogDTO ToBlogDTO (this Blog blog)
        {
            return new BlogDTO
            {
                AspectRatio = blog.AspectRatio,
                BlogPostId = blog.BlogPostId,
                Content = blog.Content,
                CreatedAt = blog.CreatedAt,
                Likes = blog.Likes,
                ModifiedAt = blog.ModifiedAt,
                Status = blog.Status,
                Subtitle = blog.Subtitle,
                ThumbnailUrl = blog.ThumbnailUrl,
                Title = blog.Title,
                UserAccountId = blog.UserAccountId,
                Views = blog.Views,
                UserAccount = blog.UserAccount.ToUserProfileDTO(),
                BlogImages = blog.BlogImages.Select(i => i.ImageUrl).ToList(),
                BlogPostTags = blog.BlogPostTags.Select(t => t.BlogTagId).ToList(),
            };
        }

        public static Blog FromCreateToBlog(this CreateBlogDTO blogDto, SystemUserAccount user)
        {
            return new Blog
            {
                AspectRatio = blogDto.AspectRatio,
                Content = blogDto.Content,
                CreatedAt = DateTime.UtcNow,
                Likes = 0,
                Status = blogDto.Status,
                ThumbnailUrl = blogDto.ThumbnailUrl,
                Subtitle = blogDto.Subtitle,
                Title = blogDto.Title,
                Views = 0,
                UserAccountId = blogDto.UserAccountId,
                BlogImages = blogDto.Image.Select(image => new BlogImage
                {
                    ImageUrl = image,

                }).ToList(),
                BlogPostTags = blogDto.TagIds.Select(tag => new BlogPostTag
                {
                    BlogTagId = tag
                }).ToList(),
            };
        }

        public static Blog FromUpdateToBlog(this UpdateBlogDTO updateBlogDTO)
        {
            return new Blog
            {

                Content = updateBlogDTO.Content,
                ModifiedAt = DateTime.UtcNow,
                Status = updateBlogDTO.Status,
                ThumbnailUrl = updateBlogDTO.ThumbnailUrl,
                Subtitle = updateBlogDTO.Subtitle,
                Title = updateBlogDTO.Title,
                BlogImages = updateBlogDTO.Image.Select(image => new BlogImage
                {
                    ImageUrl = image,

                }).ToList(),
                BlogPostTags = updateBlogDTO.TagIds.Select(tag => new BlogPostTag
                {
                    BlogTagId = tag
                }).ToList(),
            };
        }
    }
}
