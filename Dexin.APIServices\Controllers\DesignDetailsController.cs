﻿using Dexin.Service.DTOs.DesignDetailView;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DesignDetailsController : ControllerBase
    {
        private readonly IDesignDetailService _designDetailService;

        public DesignDetailsController(IDesignDetailService designDetailService)
        {
            _designDetailService = designDetailService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var result = await _designDetailService.GetAllAsync();
            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            var result = await _designDetailService.GetByIdAsync(id);
            return Ok(result);
        }

        [HttpGet("get-by-designId/{designId}")]
        public async Task<IActionResult> GetByDesignId(int designId)
        {
            var result = await _designDetailService.GetByDesignIdAsync(designId);
            return Ok(result);
        }

        [HttpPost("{designId}")]
        public async Task<IActionResult> Create(int designId, [FromBody] CreateDesignDetailDTO dto)
        {
            var result = await _designDetailService.CreateAsync(designId, dto);
            return Ok(result);
        }

        [HttpPost("create-list/{designId}")]
        public async Task<IActionResult> Create(int designId, [FromBody] List<CreateDesignDetailDTO> dtos)
        {
            var result = await _designDetailService.CreateListAsync(designId, dtos);
            return Ok(result);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int designId, [FromBody] UpdateDesignDetailDTO dto)
        {
            var result = await _designDetailService.UpdateAsync(designId, dto);
            return Ok(result);
        }

        [HttpPut("update-list/{designId}")]
        public async Task<IActionResult> UpdateListByDesignId(int designId, [FromBody] List<UpdateDesignDetailDTO> listDto)
        {
            var result = await _designDetailService.UpdateListByDesignIdAsync(designId, listDto);
            return Ok(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _designDetailService.DeleteAsync(id);
            return Ok(result);
        }
    }
}
