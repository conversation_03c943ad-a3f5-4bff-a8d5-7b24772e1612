﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.DecorItemView;

namespace Dexin.Service.Mappers
{
    public static class DecorItemMappers
    {
        public static DecorItemDTO ToDecorItemDTO(this DecorItem decorItem)
        {
            return new DecorItemDTO
            {
                DecorItemId = decorItem.DecorItemId,
                Description = decorItem.Description,
                CreatedAt = decorItem.CreatedAt,
                ImageUrl = decorItem.ImageUrl,
                ItemCategoryId = decorItem.ItemCategoryId,
                ModifiedAt = decorItem.ModifiedAt,
                Name = decorItem.Name,
                OriginHeight = decorItem.OriginHeight,
                OriginWidth = decorItem.OriginWidth,
                Status = decorItem.Status,
                ItemCategory = decorItem.ItemCategory.ToItemCategoryDTO(),

            };
        }

        public static DecorItem FromCreateToDecorItem(this CreateDecorItemDTO createDecorItemDTO)
        {
            return new DecorItem
            {
                CreatedAt = DateTime.Now,
                Description = createDecorItemDTO.Description,
                ImageUrl = createDecorItemDTO.ImageUrl,
                Name = createDecorItemDTO.Name,
                OriginHeight = createDecorItemDTO.OriginHeight,
                OriginWidth = createDecorItemDTO.OriginWidth,
                Status = createDecorItemDTO.Status,
                ItemCategoryId = createDecorItemDTO.ItemCategoryId,

            };
        }

        public static DecorItem FromUpdateToDecorItem(this UpdateDecorItemDTO updateDecorItemDTO)
        {
            return new DecorItem
            {
                Description = updateDecorItemDTO.Description,
                ModifiedAt = DateTime.Now,
                ImageUrl = updateDecorItemDTO.ImageUrl,
                Name = updateDecorItemDTO.Name,
                ItemCategoryId = updateDecorItemDTO.ItemCategoryId,
                OriginHeight = updateDecorItemDTO.OriginHeight,
                OriginWidth = updateDecorItemDTO.OriginWidth,
                Status = updateDecorItemDTO.Status,
            };
        }
    }
}
