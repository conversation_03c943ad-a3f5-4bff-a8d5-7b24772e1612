﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.ChatView;
using Dexin.Service.DTOs.User;

namespace Dexin.Service.Mappers
{
    public static class UserMapper
    {
        public static SystemUserAccount ToUserAccount(this RegisterDTO user)
        {
            return new SystemUserAccount
            {
                UserName = user.Username,
                Password = user.Password,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                Phone = "None",
                Gender = false,
                EmployeeCode = "None",
                RoleId = (int) Role.User,
                CreatedDate = DateTime.Now,
                IsActive = true,
            };

        }

        public static UserProfileDTO ToUserProfileDTO(this SystemUserAccount user)
        {
            return new UserProfileDTO
            {
                Avartar = user.Avartar,
                Email = user.Email,
                FirstName = user.FirstName,
                IsActive = user.IsActive,
                LastName = user.LastName,
                Phone = user.Phone,
                Role = user.RoleId.ToString(),
                Gender = user.Gender,
                UserAccountId = user.UserAccountId,
                UserName = user.UserName,

            };
        }

        public static SystemUserAccount FromCreateToUser(this CreateUserDTO user)
        {
            return new SystemUserAccount
            {
                UserName = user.UserName,
                Password = user.Password,
                FirstName = user.FirstName,
                Avartar = user.Avartar,
                Email = user.Email,
                IsActive = true,
                LastName = user.LastName,
                Phone = user.Phone,
                Gender = user.Gender,
                EmployeeCode = user.EmployeeCode,
                RoleId = user.RoleId,
                CreatedBy = user.CreatedBy,
            };
        }

        public static SystemUserAccount FromUpdateToUserAccount(this UpdateUserProfileDTO user)
        {
            return new SystemUserAccount
            {
               // UserAccountId = user.UserAccountId,
                UserName = user.UserName,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                Gender = user.Gender,
                IsActive = user.IsActive,
                Phone = user.Phone,

            };
        }

        public static SystemUserAccount FromUpdateAccountToUserAccount(this UpdateAccountDTO user)
        {
            return new SystemUserAccount
            {
                UserAccountId = user.UserAccountId,
                Password = user.Password,
                UserName = user.UserName,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                IsActive = user.IsActive,
                Phone = user.Phone,
                Gender = user.Gender,
                RoleId = user.RoleId,
                EmployeeCode = user.EmployeeCode,
            };
        }


        public static StaffProfileDTO ToStaffDTO(this SystemUserAccount user)
        {
            return new StaffProfileDTO
            {
                CreatedBy = user.CreatedBy,
                Avartar = user.Avartar,
                CreatedDate = user.CreatedDate,
                Email = user.Email,
                EmployeeCode = user.EmployeeCode,
                FirstName = user.FirstName,
                IsActive = user.IsActive,
                LastName = user.LastName,
                ModifiedBy = user.ModifiedBy,
                ModifiedDate = user.ModifiedDate,
                Password = user.Password,
                Phone = user.Phone,
                Gender = user.Gender,
                RoleId = user.RoleId,
                UserAccountId = user.UserAccountId,
                UserName = user.UserName,
            };
        }

        public static UserChatDTO ToUserChatDTO(this SystemUserAccount user)
        {
            return new UserChatDTO
            {
                UserAccountId = user.UserAccountId,
                Name = user.FirstName + " " + user.LastName,
                Avartar = user.Avartar
            };
        }

        public static UserChatDTO ToUserChatDTOFromProfileDTO(this UserProfileDTO user)
        {
            return new UserChatDTO
            {
                Name = user.FirstName + " " + user.LastName,
                Avartar = user.Avartar,
                UserAccountId = user.UserAccountId
            };
        }
    }
}
