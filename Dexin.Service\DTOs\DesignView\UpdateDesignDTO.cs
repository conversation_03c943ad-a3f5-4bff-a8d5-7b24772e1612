﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;

namespace Dexin.Service.DTOs.DesignView
{
    public class UpdateDesignDTO
    {
        //public int DesignId { get; set; }

        public bool? Type { get; set; }

        public string? Title { get; set; }

        public string? PathUrl { get; set; }

        public string? ThumbnailUrl { get; set; }

        public double? CanvasScale { get; set; }

        public double? CanvasPosX { get; set; }

        public double? CanvasPosY { get; set; }

        public long? Price { get; set; }
        public string? Note { get; set; }

        public ThreeDStatus Status { get; set; }

        public DateTime? ModifiedAt { get; set; }
    }
}
