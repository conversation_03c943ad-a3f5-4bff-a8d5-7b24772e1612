﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IDesignDetailRepository : IGenericRepository<DesignDetail>
    {
        Task<IEnumerable<DesignDetail>> GetAllIncludeAsync();
        Task<DesignDetail> GetByIdIncludeAsync(int id);
        Task<IEnumerable<DesignDetail>> GetByDesignIdIncludeAsync(int designId);
        Task<bool> CheckIdExisted(int id);
    }
}
