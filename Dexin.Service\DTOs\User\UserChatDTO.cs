﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Google.Cloud.Firestore;

namespace Dexin.Service.DTOs.User
{    public class UserChatDTO
    {
        public int UserAccountId { get; set; }
        public string Name { get; set; } = null!;

        //public string FirstName { get; set; } = null!;

        //public string LastName { get; set; } = null!;

        //public bool IsActive { get; set; }
        public string? Avartar { get; set; }
    
    }
}
