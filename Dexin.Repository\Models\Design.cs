﻿using System;
using System.Collections.Generic;
using Dexin.Repository.Enums;

namespace Dexin.Repository.Models;

public partial class Design
{
    public int DesignId { get; set; }

    public bool? Type { get; set; }

    public string? Title { get; set; }

    public string? PathUrl { get; set; }

    public string? ThumbnailUrl { get; set; }

    public double? CanvasScale { get; set; }

    public double? CanvasPosX { get; set; }

    public double? CanvasPosY { get; set; }

    public string? Note { get; set; }

    public long? Price { get; set; }

    public ThreeDStatus Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? CreatedBy { get; set; }

    public int? StaffId { get; set; }

    public virtual SystemUserAccount? CreatedByNavigation { get; set; }

    public virtual ICollection<DesignDetail> DesignDetails { get; set; } = new List<DesignDetail>();

    public virtual ICollection<DesignPayment> DesignPayments { get; set; } = new List<DesignPayment>();

    public virtual SystemUserAccount? Staff { get; set; }
}
