﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Microsoft.AspNetCore.Http;

namespace Dexin.Service.DTOs.User
{
    public class CreateUserWithFormImage
    {
        public string UserName { get; set; } = null!;

        public string Password { get; set; } = null!;

        public string FirstName { get; set; } = null!;

        public string LastName { get; set; } = null!;

        public string Email { get; set; } = null!;

        public string Phone { get; set; } = null!;

        public bool? Gender { get; set; }
        public string EmployeeCode { get; set; } = null!;

        //public Role RoleId { get; set; }

        public string? CreatedBy { get; set; }

        public bool IsActive { get; set; }

        public IFormFile Avartar { get; set; }
    }
}
