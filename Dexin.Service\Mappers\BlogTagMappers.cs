﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.BlogTagView;

namespace Dexin.Service.Mappers
{
    public static class BlogTagMappers
    {
        public static BlogTagDTO ToBlogTagDTO (this BlogTag blogTag)
        {
            return new BlogTagDTO
            {
                CreatedAt = blogTag.CreatedAt,
                BlogTagId = blogTag.BlogTagId,
                ModifiedAt = blogTag.ModifiedAt,
                Name = blogTag.Name,
                Status = blogTag.Status,
                BlogPostTags = blogTag.BlogPostTags.ToList(),   
            };
        }

        public static BlogTag FromCreateToBlogTag(this CreateBlogTagDTO createBlogTagDTO)
        {
            return new BlogTag
            { 
                Status = createBlogTagDTO.Status,
                Name = createBlogTagDTO.Name,
                CreatedAt = DateTime.Now,
            };
        }

        public static BlogTag FromUpdateToBlogTag(this UpdateBlogTagDTO updateBlogTagDTO)
        {
            return new BlogTag {
                Name = updateBlogTagDTO.Name,
                Status = updateBlogTagDTO.Status,
                ModifiedAt = DateTime.Now,
            };
        }
    }
}
