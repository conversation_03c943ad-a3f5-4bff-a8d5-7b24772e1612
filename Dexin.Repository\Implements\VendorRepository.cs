﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class VendorRepository : GenericRepository<Vendor>, IVendorRepository
    {
        public VendorRepository(DexinContext context)
        {
            _context = context;
        }
        public async Task<bool> CheckCompanyNameExisted(string companyName)
        {
            return await _context.Vendors.AnyAsync(c => c.CompanyName.ToLower() == companyName.ToLower());
        }

        public async Task<bool> CheckEmailExisted(string email)
        {
            return await _context.Vendors.AnyAsync(v => v.ContactEmail.ToLower() == email.ToLower());
        }

        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.Vendors.AnyAsync(c => c.VendorId == id);
        }

        public async Task<bool> CheckTaxExisted(string tax)
        {
            return await _context.Vendors.AnyAsync(v => v.TaxId == tax);
        }

        public async Task<IEnumerable<Vendor>> GetAllIncludeAsync()
        {
            return await _context.Vendors.Include(c => c.UserAccount).ToListAsync();
        }

        public async Task<Vendor> GetByIdIncludeAsync(int id)
        {
            return await _context.Vendors.Include(c => c.UserAccount).FirstOrDefaultAsync(v => v.VendorId == id);
        }
    }
}
