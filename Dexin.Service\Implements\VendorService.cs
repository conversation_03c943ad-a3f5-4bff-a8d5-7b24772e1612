﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.VendorView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class VendorService : IVendorService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IClaimsService _claimsService;
        private readonly ICurrentTime _currentTime;
        private readonly ILogger<Vendor> _logger;

        public VendorService(IUnitOfWork unitOfWork, IClaimsService claimsService, ICurrentTime currentTime, ILogger<Vendor> logger)
        {
            _claimsService = claimsService;
            _currentTime = currentTime;
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        public async Task<ResponseModels<CreateVendorDTO>> CreateAsync(CreateVendorDTO vendorDTO)
        {
            var response = new ResponseModels<CreateVendorDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                if (await _unitOfWork.VendorRepository.CheckCompanyNameExisted(vendorDTO.CompanyName))
                {
                    response.Message = "Tên Công ty đã trùng!";
                    response.Success = true;
                    return response;
                }

                if (await _unitOfWork.VendorRepository.CheckEmailExisted(vendorDTO.ContactEmail))
                {
                    response.Message = "Email này đã tồn tại!";
                    response.Success = true;
                    return response;
                }

                if (await _unitOfWork.VendorRepository.CheckTaxExisted(vendorDTO.TaxId))
                {
                    response.Message = "Mã số thuế đã tồn tại!";
                    response.Success = true;
                    return response;
                }

                var model = vendorDTO.FromCreateToVendor();
                model.CreatedAt = _currentTime.GetCurrentTime();

                var rs = await _unitOfWork.VendorRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Message = "Tạo mới Công ty thành công!";
                    response.Success = true;
                    response.Data = vendorDTO;
                    return response;
                }

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra";
                _logger.LogError(ex, "Create Error: ");
            }
            return response;
        }

        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false
            };

            try
            {

                var model = await _unitOfWork.VendorRepository.GetByIdAsync(id);

                var rs = await _unitOfWork.VendorRepository.RemoveAsync(model);
                if (rs)
                {
                    response.Message = "Xóa thành công!";
                    response.Success = true;
                    response.Data = true;
                    return response;
                }

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra";
                _logger.LogError(ex, "Create Error: ");
            }
            return response;
        }

        public Task<ResponseModels<IEnumerable<VendorDTO>>> GetAllAsync()
        {
            throw new NotImplementedException();
        }

        public Task<ResponseModels<VendorDTO>> GetByIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<ResponseModels<VendorDTO>> UpdateAsync(UpdateVendorDTO vendorDTO)
        {
            throw new NotImplementedException();
        }
    }
}
