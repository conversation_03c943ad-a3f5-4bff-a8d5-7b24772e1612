using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DashboardView;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Manager, Staff")]
    public class DashboardController : ControllerBase
    {
        private readonly IDashboardService _dashboardService;

        public DashboardController(IDashboardService dashboardService)
        {
            _dashboardService = dashboardService;
        }

        /// <summary>
        /// L<PERSON>y thống kê tổng quan cho dashboard admin
        /// </summary>
        /// <returns>Thống kê tổng quan bao gồm tổng số users, orders, revenue, designs</returns>
        [HttpGet("overview")]
        public async Task<ActionResult<ResponseModels<DashboardOverviewDTO>>> GetDashboardOverview()
        {
            var result = await _dashboardService.GetDashboardOverviewAsync();
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Lấy thống kê người dùng theo thời gian
        /// </summary>
        /// <param name="period">Khoảng thời gian: day, week, month, year (mặc định: month)</param>
        /// <returns>Thống kê người dùng bao gồm người dùng mới, phân bố theo role, hoạt động gần đây</returns>
        [HttpGet("user-statistics")]
        public async Task<ActionResult<ResponseModels<UserStatisticsDTO>>> GetUserStatistics([FromQuery] string period = "month")
        {
            var result = await _dashboardService.GetUserStatisticsAsync(period);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Lấy thống kê giao dịch và doanh thu theo thời gian
        /// </summary>
        /// <param name="period">Khoảng thời gian: day, week, month, year (mặc định: month)</param>
        /// <returns>Thống kê giao dịch bao gồm doanh thu, số lượng giao dịch Design/Product Payment, trạng thái thanh toán</returns>
        [HttpGet("transaction-statistics")]
        public async Task<ActionResult<ResponseModels<TransactionStatisticsDTO>>> GetTransactionStatistics([FromQuery] string period = "month")
        {
            var result = await _dashboardService.GetTransactionStatisticsAsync(period);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
    }
}
