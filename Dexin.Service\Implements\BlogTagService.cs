﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.BlogTagView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class BlogTagService : IBlogTagService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<BlogTag> _logger;
        public BlogTagService(IUnitOfWork unitOfWork, ILogger<BlogTag> logger)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        public async Task<ResponseModels<CreateBlogTagDTO>> CreateAsync(CreateBlogTagDTO blogTagDTO)
        {
            var response = new ResponseModels<CreateBlogTagDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {

                if (await _unitOfWork.BlogTagRepository.CheckNameExisted(blogTagDTO.Name))
                {
                    response.Message = "Thẻ đã tồn tại! Vui lòng nhập tên khác!";
                    return response;
                }

                var rs = await _unitOfWork.BlogTagRepository.CreateAsync(blogTagDTO.FromCreateToBlogTag());
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Thêm mới thẻ thành công!";
                    response.Data = blogTagDTO;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Create Error");
                return response;
            }

        }

        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false
            };

            try
            {

                var tag = await _unitOfWork.BlogTagRepository.GetByIdAsync(id);
                if (tag == null)
                {
                    response.Message = "Không tìm thấy dữ liệu để xóa! Vui lòng thử lại!";
                    return response;
                }
                var rs = await _unitOfWork.BlogTagRepository.RemoveAsync(tag);
                if (rs)
                {
                    response.Success = true;
                    response.Message = "Xóa thành công!";
                    response.Data = rs;
                    return response;
                }
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Delete Error");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<BlogTagDTO>>> GetAllAsync()
        {
            var response = new ResponseModels<IEnumerable<BlogTagDTO>>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var tags = await _unitOfWork.BlogTagRepository.GetAllIncludeAsync();
                if (tags == null)
                {
                    response.Message = "Không có dữ liệu!";
                    return response;
                }

                response.Success = true;
                response.Message = "Thành công!";
                response.Data = tags.Select(t => t.ToBlogTagDTO()).ToList();
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "GetAll Error");
                return response;
            }

        }

        public async Task<ResponseModels<BlogTagDTO>> GetByIdAsync(int id)
        {
            var response = new ResponseModels<BlogTagDTO>()
            {
                Success = false,
                Data = null
            };
            var tag = await _unitOfWork.BlogTagRepository.GetByIdIncludeAsync(id);
            try
            {
                if (tag == null)
                {
                    response.Message = "Không có dữ liệu!";
                    return response;

                }

                response.Success = true;
                response.Message = "Thành công!";
                response.Data = tag.ToBlogTagDTO();
                return response;

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Get Error");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<BlogTagDTO>>> SearchByNameAsync(string name)
        {
            var response = new ResponseModels<IEnumerable<BlogTagDTO>>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var tags = await _unitOfWork.BlogTagRepository.SearchByNameIncludeAsync(name);
                if (tags == null)
                {
                    response.Message = "Không có dữ liệu!";
                    return response;
                }
                response.Success = true;
                response.Message = "Thành công!";
                response.Data = tags.Select(t => t.ToBlogTagDTO()).ToList();
                return response;

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Search Error");
                return response;
            }
        }

        public async Task<ResponseModels<UpdateBlogTagDTO>> UpdateByBlogTagAsync(int id, UpdateBlogTagDTO blogTagDTO)
        {
            var response = new ResponseModels<UpdateBlogTagDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var tag = await _unitOfWork.BlogTagRepository.GetByIdIncludeAsync(id);
                if (tag == null)
                {
                    response.Message = "Không có dữ liệu!";
                    return response;
                }

                var tagUpdate = blogTagDTO.FromUpdateToBlogTag();
                tagUpdate.CreatedAt = tag.CreatedAt;
                tagUpdate.BlogTagId = id;

                response.Success = true;
                response.Message = "Cập nhật thành công!";
                response.Data = blogTagDTO;
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra!";
                _logger.LogError(ex, "Update     Error");
                return response;
            }

        }
    }
}
