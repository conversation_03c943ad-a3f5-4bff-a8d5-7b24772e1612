﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DesignView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class DesignService : IDesignService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IClaimsService _claimsService;
        private readonly ILogger<Design> _logger;
        private readonly ICurrentTime _currentTime;
        public DesignService(IUnitOfWork unitOfWork, ILogger<Design> logger, IClaimsService claimsService, ICurrentTime currentTime)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _claimsService = claimsService;
            _currentTime = currentTime;
        }
        public async Task<ResponseModels<CreateDesignDTO>> Create2DDesign(CreateDesignDTO designDTO)
        {
            var response = new ResponseModels<CreateDesignDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = designDTO.FromCreateToDesign();
                model.Type = false;
                model.StaffId = _claimsService.GetCurrentUserId;
                var rs = await _unitOfWork.DesignRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Message = "Thêm mới thành công";
                    response.Success = true;
                    response.Data = designDTO;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Create Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<Create3DDTO>> Create3DDesign(Create3DDTO designDTO)
        {
            var response = new ResponseModels<Create3DDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = designDTO.FromCreate3DToDesign();
                model.StaffId = _claimsService.GetCurrentUserId;
                var rs = await _unitOfWork.DesignRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Message = "Thêm mới thành công";
                    response.Success = true;
                    response.Data = designDTO;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Create Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<CreateDesignDTO>> CreateAsync(CreateDesignDTO designDTO)
        {
            var response = new ResponseModels<CreateDesignDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = designDTO.FromCreateToDesign();
                model.StaffId = _claimsService.GetCurrentUserId;
                var rs = await _unitOfWork.DesignRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Message = "Thêm mới thành công";
                    response.Success = true;
                    response.Data = designDTO;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Create Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = true
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetByIdAsync(id);

                if (await _unitOfWork.DesignRepository.RemoveAsync(model))
                {
                    response.Message = "Xóa thành công";
                    response.Success = true;
                    response.Data = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Delete Error: ");
                return response;
            }

        }

        public Task<ResponseModels<IEnumerable<DesignDTO>>> Get2DDesigns()
        {
            throw new NotImplementedException();
        }

        public Task<ResponseModels<IEnumerable<DesignDTO>>> Get3DDesigns()
        {
            throw new NotImplementedException();
        }

        public async Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllAsync()
        {
            var response = new ResponseModels<IEnumerable<DesignDTO>>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetAllIncludeAsync();

                if (model != null)
                {
                    response.Message = "Thành công!";
                    response.Success = true;
                    response.Data = model.Select(d => d.ToDesignDTO());
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllByStaffAndUserAsync(int staffId, int userId)
        {
            var response = new ResponseModels<IEnumerable<DesignDTO>>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetAllByUserAndStaffIdAsync(staffId, userId);

                if (model != null)
                {
                    response.Message = "Thành công!";
                    response.Success = true;
                    response.Data = model.Select(d => d.ToDesignDTO());
                    return response;
                }

                if (model == null)
                {
                    response.Message = "Không tìm thấy dữ liệu!";
                    response.Success = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllByStaffIdAsync(int staffId)
        {
            var response = new ResponseModels<IEnumerable<DesignDTO>>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetAllByStaffIdAsync(staffId);

                if (model != null)
                {
                    response.Message = "Thành công!";
                    response.Success = true;
                    response.Data = model.Select(d => d.ToDesignDTO());
                    return response;
                }

                if (model == null)
                {
                    response.Message = "Không tìm thấy dữ liệu!";
                    response.Success = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllByUserIdAsync(int userId)
        {
            var response = new ResponseModels<IEnumerable<DesignDTO>>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetAllByUserIdAsync(userId);

                if (model != null)
                {
                    response.Message = "Thành công!";
                    response.Success = true;
                    response.Data = model.Select(d => d.ToDesignDTO());
                    return response;
                }

                if (model == null)
                {
                    response.Message = "Không tìm thấy dữ liệu!";
                    response.Success = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<DesignDTO>> GetByIdAsync(int id)
        {
            var response = new ResponseModels<DesignDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetByIdIncludeAsync(id);

                if (model != null)
                {
                    response.Message = "Thành công!";
                    response.Success = true;
                    response.Data = model.ToDesignDTO();
                    return response;
                }

                if (model == null)
                {
                    response.Message = "Không tìm thấy dữ liệu!";
                    response.Success = true;
                    return response;
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> Udpate3DToCustomer(int designId, string pathUrl)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false,
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetByIdIncludeAsync(designId);
                if (model == null)
                {
                    response.Success = true;
                    response.Message = "Không tìm thấy dữ liệu để cập nhật!";
                    return response;
                }
                else
                {
                  
                    model.PathUrl = pathUrl;
                    model.ModifiedAt = _currentTime.GetCurrentTime();
                    var rs = await _unitOfWork.DesignRepository.UpdateAsync(model);
                    if (rs > 0)
                    {
                        response.Success = true;
                        response.Message = "Cập nhật thành công!";
                        response.Data = true;
                        return response;
                    }
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Update Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<UpdateDesignDTO>> UpdateAsync(int id, UpdateDesignDTO updateDesignDTO)
        {
            var response = new ResponseModels<UpdateDesignDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetByIdIncludeAsync(id);
                if(model == null)
                {
                    response.Success = true;
                    response.Message = "Không tìm thấy dữ liệu để cập nhật!";
                    return response;
                }
                else
                {
                    var updateModel = updateDesignDTO.FromUpdateToDesign();
                    updateModel.DesignId = id;
                    updateModel.ModifiedAt = _currentTime.GetCurrentTime();
                    var rs = await _unitOfWork.DesignRepository.UpdateAsync(updateModel);
                    if(rs >0)
                    {
                        response.Success= true;
                        response.Message = "Cập nhật thành công!";
                        response.Data = updateDesignDTO;
                        return response;
                    }
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Update Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<bool>> Update3DStatus(int designId, ThreeDStatus status)
        {
            var response = new ResponseModels<bool>()
            {
                Success = false,
                Data = false,
            };

            try
            {
                var model = await _unitOfWork.DesignRepository.GetByIdIncludeAsync(designId);
                if (model == null)
                {
                    response.Success = true;
                    response.Message = "Không tìm thấy dữ liệu để cập nhật!";
                    return response;
                }
                else
                {

                    model.Status = status;
                    model.ModifiedAt = _currentTime.GetCurrentTime();
                    var rs = await _unitOfWork.DesignRepository.UpdateAsync(model);
                    if (rs > 0)
                    {
                        response.Success = true;
                        response.Message = "Cập nhật thành công!";
                        response.Data = true;
                        return response;
                    }
                }

                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Update Error: ");
                return response;
            }
        }
    }
}
