﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.DesignPaymentView;
using Net.payOS.Types;

namespace Dexin.Service.Mappers
{
    public static class DesignPaymentMappers
    {
        public static DesignPaymentDTO ToDesignPaymentDTO(this DesignPayment designPayment)
        {
            return new DesignPaymentDTO
            {
                Amount = designPayment.Amount,
                CurrencyCode = designPayment.CurrencyCode,
                CreatedAt = designPayment.CreatedAt,
                DesignId = designPayment.DesignId,
                GatewayResponse = designPayment.GatewayResponse,
                DesignPaymentId = designPayment.DesignPaymentId,
                ModifiedAt = designPayment.ModifiedAt,
                PaymentDate = designPayment.PaymentDate,
                PaymentMethod = designPayment.PaymentMethod,
                PaymentProvide = designPayment.PaymentProvide,
                Status = designPayment.Status,
                TransactionIdGateway = designPayment.TransactionIdGateway,
                UserAccount = designPayment.UserAccount,
                UserAccountId = designPayment.UserAccountId,
                Design = designPayment.Design,
            };
        }

        public static DesignPayment FromCreateToDesignPayment(this CreateDesignPaymentDTO designPayment)
        {
            return new DesignPayment
            {
                Amount = designPayment.Amount,
                CurrencyCode = designPayment.CurrencyCode,
                DesignId = designPayment.DesignId,
                GatewayResponse = designPayment.GatewayResponse,
                PaymentMethod = designPayment.PaymentMethod,
                PaymentProvide = designPayment.PaymentProvide,
                Status = designPayment.Status,
                TransactionIdGateway = designPayment.TransactionIdGateway,
                PaymentDate = designPayment.PaymentDate,
            };
        }

        public static CreateDesignPaymentDTO FromResultLinkPayOSToCreateDTO(this CreatePaymentResult createPaymentResult)
        {
            return new CreateDesignPaymentDTO
            {
                Amount = createPaymentResult.amount,
                CurrencyCode = createPaymentResult.currency,
                TransactionIdGateway = createPaymentResult.orderCode.ToString(),
                Status = createPaymentResult.status,
                GatewayResponse = createPaymentResult.description,
                PaymentDate = DateTime.UtcNow,
                PaymentMethod = "QR Code",
                PaymentProvide = "PayOS"
            };
        }
    }

}
