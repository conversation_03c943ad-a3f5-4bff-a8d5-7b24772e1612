﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class ItemCategoryRepository : GenericRepository<ItemCategory>, IItemCategoryRepository
    {
        public ItemCategoryRepository()
        {

        }
        public ItemCategoryRepository(DexinContext context) => _context = context;
        public async Task<bool> CheckNameExisted(string name)
        {
            return await _context.ItemCategories.AnyAsync(i => i.Name.ToLower() == name.ToLower());
        }

        public async Task<bool> CheckSlugExisted(string slug)
        {
            return await _context.ItemCategories.AnyAsync(i => i.Slug.ToLower() == slug.ToLower()); 
        } 
        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.ItemCategories.AnyAsync(i => i.ItemCategoryId == id); 
        }

        
    }
}
