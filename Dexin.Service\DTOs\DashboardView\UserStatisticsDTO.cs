using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.DTOs.DashboardView
{
    public class UserStatisticsDTO
    {
        public int TotalUsers { get; set; }
        public int NewUsersThisMonth { get; set; }
        public int NewUsersThisWeek { get; set; }
        public int NewUsersToday { get; set; }
        public int ActiveUsers { get; set; }
        public UserRoleDistributionDTO RoleDistribution { get; set; } = new UserRoleDistributionDTO();
        public List<UserGrowthDataDTO> UserGrowthData { get; set; } = new List<UserGrowthDataDTO>();
        public List<RecentUserDTO> RecentUsers { get; set; } = new List<RecentUserDTO>();
    }

    public class UserRoleDistributionDTO
    {
        public int Users { get; set; }
        public int Staff { get; set; }
        public int Managers { get; set; }
        public int Vendors { get; set; }
    }

    public class UserGrowthDataDTO
    {
        public DateTime Date { get; set; }
        public int NewUsers { get; set; }
        public int TotalUsers { get; set; }
    }

    public class RecentUserDTO
    {
        public int UserAccountId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public DateTime? CreatedDate { get; set; }
        public string? Avatar { get; set; }
    }
}
