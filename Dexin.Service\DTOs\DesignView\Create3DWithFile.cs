﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Dexin.Service.DTOs.DesignView
{
    public class Create3DWithFile
    {
        public string? Title { get; set; }


        public IFormFile? PathUrl { get; set; }

        public IFormFile ThumbnailUrl { get; set; }

        public string? Note { get; set; }

        public long? Price { get; set; }

        public int? CustomerId { get; set; }

       // public int? StaffId { get; set; }
    }
}
