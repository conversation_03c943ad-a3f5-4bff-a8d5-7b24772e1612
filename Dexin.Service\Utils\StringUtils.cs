﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.Utils
{
    public static class StringUtils
    {
        public static string Hash(this string input)
        {
            SHA256 hash256 = SHA256.Create();
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);

            byte[] hash = hash256.ComputeHash(inputBytes);

            IEnumerable<string> hex = hash.Select(x => x.ToString("x2")); // <PERSON><PERSON><PERSON><PERSON> sang cơ số 16(hex) nó sẽ trả về 64 ký tự hex

            string passwordHash = string.Join("", hex);
            return passwordHash;
        }
    }
}
