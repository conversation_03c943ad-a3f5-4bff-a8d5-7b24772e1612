﻿
using Dexin.Repository.Enums;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;


namespace Dexin.Service.Implements
{
    public class ClaimsService : IClaimsService
    {
        public ClaimsService(IHttpContextAccessor httpContextAccessor)
        {
            var userId = httpContextAccessor.HttpContext?.User?.FindFirst("UserId")?.Value;
            GetCurrentUserId = string.IsNullOrEmpty(userId) ? 0 : int.Parse(userId);

            var userName = httpContextAccessor.HttpContext?.User?.FindFirst("UserName")?.Value;
            GetCurrentUserName = string.IsNullOrEmpty(userName) ? "Unknown" : userName;

            var role = httpContextAccessor.HttpContext?.User?.FindFirst("Role")?.Value;
            GetCurrentRole = string.IsNullOrEmpty(role) ? "Unknown" : role;
        }

        public int GetCurrentUserId
        {
            get;
        }

        public string GetCurrentUserName
        {
            get;
        }

        public string GetCurrentRole
        { 
            get; 
        }


    }
}
