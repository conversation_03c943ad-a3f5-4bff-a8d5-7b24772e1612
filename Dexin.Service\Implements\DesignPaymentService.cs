﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Design.Serialization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DesignPaymentView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class DesignPaymentService : IDesignPaymentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IClaimsService _claimsService;
        private readonly ICurrentTime _currentTime;
        private readonly ILogger<DesignPayment> _logger;

        public DesignPaymentService(IUnitOfWork unitOfWork, IClaimsService claimsService, ICurrentTime currentTime, ILogger<DesignPayment> logger)
        {
            _unitOfWork = unitOfWork;
            _claimsService = claimsService;
            _currentTime = currentTime;
            _logger = logger;
        }

        public async Task<ResponseModels<DesignPaymentDTO>> CreateAsync(CreateDesignPaymentDTO designPaymentDTO)
        {
            var response = new ResponseModels<DesignPaymentDTO>()
            {
                Success = false,
                Data = null,
            };

            try
            {
                var model = designPaymentDTO.FromCreateToDesignPayment();
                model.CreatedAt = _currentTime.GetCurrentTime();
                model.UserAccountId = _claimsService.GetCurrentUserId;

                var rs = await _unitOfWork.DesignPaymentRepository.CreateAsync(model);
                if (rs > 0)
                {
                    response.Success = true;
                    response.Message = "Tạo bản ghi thanh toán thành công!";
                    response.Data = model.ToDesignPaymentDTO();
                    return response;
                }


                response.Message = "Đã có lỗi xảy ra!";
                return response;
            }
            catch (Exception ex)
            {

                response.Message = "Đã có lỗi xảy ra! Thử lại sau!";
                _logger.LogError(ex, "Create Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntAsync()
        {
            var response = new ResponseModels<IEnumerable<DesignPaymentDTO>>()
            {
                Success = false,
                Data = null,
            };

            try
            {
                var list = await _unitOfWork.DesignPaymentRepository.GetAllIncludeAsync();

                if (list.Count() > 0)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = list.Select(p => p.ToDesignPaymentDTO()).ToList();
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                    return response;
                }

            }
            catch (Exception ex)
            {

                response.Message = "Đã có lỗi xảy ra! Thử lại sau!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntByDesignIdAsync(int designId)
        {
            var response = new ResponseModels<IEnumerable<DesignPaymentDTO>>()
            {
                Success = false,
                Data = null,
            };

            try
            {
                var list = await _unitOfWork.DesignPaymentRepository.GetAllIncludeAsync();
                var rs = list.Where(p => p.DesignId == designId);

                if (rs.Count() > 0)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = rs.Select(p => p.ToDesignPaymentDTO()).ToList();
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                    return response;
                }

            }
            catch (Exception ex)
            {

                response.Message = "Đã có lỗi xảy ra! Thử lại sau!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntByTransactionIdAsync(string transactionId)
        {
            var response = new ResponseModels<IEnumerable<DesignPaymentDTO>>()
            {
                Success = false,
                Data = null,
            };

            try
            {
                var list = await _unitOfWork.DesignPaymentRepository.GetAllIncludeAsync();
                var rs = list.Where(p => p.TransactionIdGateway.Equals(transactionId));

                if (rs.Count() > 0)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = rs.Select(p => p.ToDesignPaymentDTO()).ToList();
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                    return response;
                }

            }
            catch (Exception ex)
            {

                response.Message = "Đã có lỗi xảy ra! Thử lại sau!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<IEnumerable<DesignPaymentDTO>>> GetAllDesingPayemntByUserIdAsync(int userId)
        {
            var response = new ResponseModels<IEnumerable<DesignPaymentDTO>>()
            {
                Success = false,
                Data = null,
            };

            try
            {
                var list = await _unitOfWork.DesignPaymentRepository.GetAllIncludeAsync();
                var rs = list.Where(p => p.UserAccountId == userId);

                if (rs.Count() > 0)
                {
                    response.Success = true;
                    response.Message = "Thành công!";
                    response.Data = rs.Select(p => p.ToDesignPaymentDTO()).ToList();
                    return response;
                }

                else
                {
                    response.Success = true;
                    response.Message = "Không có dữ liệu";
                    response.Data = null;
                    return response;
                }

            }
            catch (Exception ex)
            {

                response.Message = "Đã có lỗi xảy ra! Thử lại sau!";
                _logger.LogError(ex, "Get Error: ");
                return response;
            }
        }

        public async Task<ResponseModels<DesignPaymentDTO>> UpdatePaymetnStatusAsync(string transactionIdGateway, string status)
        {
            var response = new ResponseModels<DesignPaymentDTO>()
            {
                Success = false,
                Message = "Có lỗi xảy ra trong quá trình xử lý.", 
                Data = null,
            };

            try
            {
                var payment = await _unitOfWork.DesignPaymentRepository.GetByTransactionIdAsync(transactionIdGateway);

                if (payment == null)
                {
              
                    response.Success = false; 
                    response.Message = "Không tìm thấy giao dịch với mã tương ứng.";
                    return response;
                }

                var design = await _unitOfWork.DesignRepository.GetByIdAsync((int) payment.DesignId);

                if (design == null)
                {
                    response.Message = "Không tìm thấy bản thiết kế liên quan đến thanh toán này.";
                    return response;
                }

                payment.Status = status;
                payment.ModifiedAt = DateTime.Now;

                var rs1 = 0;
                switch (status)
                {
                    case "PAID":
                        design.Status = ThreeDStatus.Paid;
                        rs1 = await _unitOfWork.DesignPaymentRepository.UpdateAsync(payment);
                        break;
                    default:
                        rs1 = await _unitOfWork.DesignPaymentRepository.UpdateAsync(payment);
                        break;
                }

               
                var rs2 = await _unitOfWork.DesignRepository.UpdateAsync(design);

                var result = await _unitOfWork.CommitAsync();

                if (rs1 > 0 && rs2 > 0)
                {
                    response.Success = true;
                    response.Message = "Cập nhật trạng thái thanh toán thành công!";
                    response.Data = payment.ToDesignPaymentDTO();
                }
                else
                {
                    response.Message = "Cập nhật thất bại, không có thay đổi nào được lưu.";
                }
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi hệ thống xảy ra! Thử lại sau!";
                _logger.LogError(ex, "Lỗi khi cập nhật trạng thái thanh toán với Transaction ID: {TransactionId}", transactionIdGateway);
            }

            return response;
        }
    }
}
