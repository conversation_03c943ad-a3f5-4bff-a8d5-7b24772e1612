﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.DBContext;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;

namespace Dexin.Repository.Implements
{
    public class BlogTagRepository : GenericRepository<BlogTag>, IBlogTagRepository
    {
        public BlogTagRepository() { }
        public BlogTagRepository(DexinContext context) => _context = context;
        public async Task<bool> CheckIdExisted(int id)
        {
            return await _context.BlogTags.AnyAsync(b => b.BlogTagId == id);
        }

        public async Task<bool> CheckNameExisted(string name)
        {
            return await _context.BlogTags.AnyAsync(b => b.Name.ToLower() == name.ToLower());
        }

        public async Task<IEnumerable<BlogTag>> GetAllIncludeAsync()
        {
            return await _context.BlogTags.Include(b => b.BlogPostTags).ThenInclude(b => b.BlogPost).ToListAsync();
        }

        public async Task<BlogTag> GetByIdIncludeAsync(int id)
        {
           return await _context.BlogTags.Include(b => b.BlogPostTags).FirstOrDefaultAsync(b => b.BlogTagId == id);
        }

        public async Task<IEnumerable<BlogTag>> SearchByNameIncludeAsync(string name)
        {
            var tags = await _context.BlogTags.Include(b => b.BlogPostTags).Where(b => b.Name.Contains(name)).ToListAsync();
            return tags;
        }
    }
}
