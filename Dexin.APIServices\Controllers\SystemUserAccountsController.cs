﻿using Dexin.Repository.Enums;
using Dexin.Service.DTOs.User;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.IdentityModel.Tokens.Jwt;
using System.Threading.Tasks;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SystemUserAccountsController : ControllerBase
    {
        private readonly ISystemUserAccountService _systemUserAccountService;
        private readonly IImageService _imageService;
        private readonly ITokenBlacklistService _tokenBlacklistService;

        public SystemUserAccountsController(ISystemUserAccountService systemUserAccountService, IImageService imageService, ITokenBlacklistService tokenBlacklistService)
        {
            _systemUserAccountService = systemUserAccountService;
            _imageService = imageService;
            _tokenBlacklistService = tokenBlacklistService;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login(LoginDTO userLoginDTO)
        {
            var response = await _systemUserAccountService.LoginAsync(userLoginDTO);
            if (response.Success)
                return Ok(response.Data);

            return Unauthorized(response.Message);
        }

        [HttpPost("manager-login")]
        public async Task<IActionResult> LoginManagerAccountAsync(LoginDTO accountObject)
        {
            var response = await _systemUserAccountService.LoginManagerAccountAsync(accountObject);
            if (response.Success)
            {
                return Ok(response.Data);
            }
            return BadRequest(response.Message);
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register(RegisterDTO registerDTO)
        {
            var response = await _systemUserAccountService.RegisterAsync(registerDTO);
            if (response.Success)
                return Ok(response.Message);

            return BadRequest(response.Message);
        }


        [HttpPost("create-staff")]
        public async Task<IActionResult> CreateStaffAccount([FromForm] CreateUserWithFormImage registerDTO)
        {
            var imageRs = await _imageService.UploadImageAsync(registerDTO.Avartar);

            var dto = new CreateUserDTO
            {
                UserName = registerDTO.UserName,
                Avartar = imageRs,
                Email = registerDTO.Email,
                CreatedBy = registerDTO.CreatedBy,
                EmployeeCode = registerDTO.EmployeeCode,
                FirstName = registerDTO.FirstName,
                IsActive = registerDTO.IsActive,
                LastName = registerDTO.LastName,
                Password = registerDTO.Password,
                Phone = registerDTO.Phone,
                Gender = registerDTO.Gender,
                RoleId = Role.Staff,
            };

            var response = await _systemUserAccountService.CreateUserAsync(dto);
            if (response.Success)
                return Ok(response);

            return BadRequest(response.Message);
        }

        [HttpPost("create-vendor")]
        public async Task<IActionResult> CreateVendorAccount([FromForm] CreateUserWithFormImage registerDTO)
        {
            var imageRs = await _imageService.UploadImageAsync(registerDTO.Avartar);

            var dto = new CreateUserDTO
            {
                UserName = registerDTO.UserName,
                Avartar = imageRs,
                Email = registerDTO.Email,
                CreatedBy = registerDTO.CreatedBy,
                EmployeeCode = registerDTO.EmployeeCode,
                FirstName = registerDTO.FirstName,
                IsActive = registerDTO.IsActive,
                LastName = registerDTO.LastName,
                Password = registerDTO.Password,
                Phone = registerDTO.Phone,
                Gender = registerDTO.Gender,
                RoleId = Role.Vendor,
            };

            var response = await _systemUserAccountService.CreateUserAsync(dto);
            if (response.Success)
                return Ok(response);

            return BadRequest(response.Message);
        }

        [HttpGet("get-by-id/{id}")]
        public async Task<IActionResult> GetUserById(int id)
        {
            var response = await _systemUserAccountService.GetUserProfileByIdAsync(id);
            if (response.Success)
                return Ok(response);

            return NotFound(response.Message);
        }

        [HttpGet("get-all-customers")]
        public async Task<IActionResult> GetAllCustomes()
        {
            var response = await _systemUserAccountService.GetAllCustomerAsync();
            if (response.Success)
            {
                if (response.Data != null)
                    return Ok(response);
                else
                    return NotFound();
            }

            return BadRequest(response);
        }

        [HttpGet("get-all-staffs")]
        public async Task<IActionResult> GetAllStaffs()
        {
            var response = await _systemUserAccountService.GetAllStaffProfilesAsync();
            if (response.Success)
            {
                if (response.Data != null)
                    return Ok(response);
                else
                    return NotFound();
            }

            return BadRequest(response);
        }

        [HttpPut("update-profile/{id}")]
        public async Task<IActionResult> UpdateUserProfile([FromBody] UpdateUserProfileDTO profileDTO, int id)
        {
            var response = await _systemUserAccountService.UpdateUserProfileAsync(profileDTO, id);
            if (response.Success)
                return Ok(response);

            return BadRequest(response.Message);
        }

        [HttpPut("update-account/{id}")]
        public async Task<IActionResult> UpdateAccount([FromBody] UpdateAccountDTO accountDTO, int id)
        {
            var response = await _systemUserAccountService.UpdateAccountAsync(accountDTO, id);
            if (response.Success)
                return Ok(response);

            return BadRequest(response.Message);
        }

        [HttpPut("update-avatar/{userId}")]
        [Authorize]
        public async Task<IActionResult> UpdateUserAvatar(int userId, IFormFile avatar)
        {
            var imageUrl = await _imageService.UploadImageAsync(avatar);
            var response = await _systemUserAccountService.UpdateUserImageAsync(userId, imageUrl);
            if (response.Success)
                return Ok(response);

            return BadRequest(response.Message);
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var response = await _systemUserAccountService.DeleteAsync(id);
            if (response.Success)
                return Ok(response);
            return BadRequest(response.Message);
        }


        [Authorize]
        [HttpPost("logout")]
        public async Task<IActionResult> Logout()
        {
            var authorizationHeader = HttpContext.Request.Headers["Authorization"].FirstOrDefault();
            string currentToken = null;

            if (!string.IsNullOrEmpty(authorizationHeader) && authorizationHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                currentToken = authorizationHeader.Substring("Bearer ".Length).Trim();
            }

            if (string.IsNullOrEmpty(currentToken))
            {
                return Unauthorized(new { message = "No token provided for logout." });
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            DateTime? tokenExpiration = null;

            if (tokenHandler.CanReadToken(currentToken))
            {
                var jwtSecurityToken = tokenHandler.ReadToken(currentToken) as JwtSecurityToken;
                if (jwtSecurityToken != null)
                {
                    tokenExpiration = jwtSecurityToken.ValidTo;
                }
            }

            if (tokenExpiration == null || tokenExpiration <= DateTime.UtcNow)
            {
                return BadRequest(new { message = "The provided token is expired or invalid, no logout action needed." });
            }

            await _tokenBlacklistService.AddToBlacklistAsync(currentToken, tokenExpiration.Value);

            var userId = User.FindFirst("UserId")?.Value;
            var userName = User.FindFirst("UserName")?.Value;
            Console.WriteLine($"User '{userName}' (ID: {userId}) successfully blacklisted JWT at {DateTime.UtcNow}.");

            return Ok(new { message = "Logged out successfully." });
        }
    }
}
