﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.Commons;
using Dexin.Service.DTOs.ItemCategoryView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class ItemCategoryService : IItemCategoryService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentTime _currentTime;
        private readonly ILogger<ItemCategoryService> _logger;


        public ItemCategoryService(IUnitOfWork unitOfWork, ICurrentTime currentTime, ILogger<ItemCategoryService> logger)
        {
            _currentTime = currentTime;
            _unitOfWork = unitOfWork;
            _logger = logger;   
        }

        public async Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            var response = new ResponseModels<bool>
            {
                Data = false,
                Success = false
            };

            try
            {
                var item = await _unitOfWork.ItemCategoryRepository.GetByIdAsync(id);
                if (item == null)
                {
                    response.Message = "Không tìm thấy danh mục cần xóa!";
                    return response;
                }

                response.Data = await _unitOfWork.ItemCategoryRepository.RemoveAsync(item);
                response.Success = true;
                response.Message = "Xóa thành công";
            }
            catch (Exception ex)
            {
                response.Message = $"Lỗi khi xóa danh mục: {ex.Message}";
            }

            return response;
        }

        public async Task<ResponseModels<IEnumerable<ItemCategoryDTO>>> GetAllAsync()
        {
            var response = new ResponseModels<IEnumerable<ItemCategoryDTO>>
            {
                Success = false,
                Data = null
            };

            try
            {
                var items = await _unitOfWork.ItemCategoryRepository.GetAllAsync();

                if (items == null || !items.Any())
                {
                    response.Message = "Không tìm thấy dữ liệu!";
                    return response;
                }

                var result = items.Select(item => item.ToItemCategoryDTO());

                response.Data = result;
                response.Success = true;
                response.Message = "Lấy danh sách danh mục thành công";
            }
            catch (Exception ex)
            {
                response.Message = $"Lỗi khi lấy dữ liệu: {ex.Message}";
            }

            return response;
        }

        public async Task<ResponseModels<ItemCategoryDTO>> GetByIdAsync(int id)
        {
            var response = new ResponseModels<ItemCategoryDTO>
            {
                Data = null,
                Success = false
            };

            try
            {
                var item = await _unitOfWork.ItemCategoryRepository.GetByIdAsync(id);
                if (item == null)
                {
                    response.Message = "Không tìm thấy dữ liệu!";
                    return response;
                }

                response.Data = item.ToItemCategoryDTO();
                response.Success = true;
                response.Message = "Thành công";
                return response;
            }
            catch (Exception ex) 
            {
                response.Success= false;
                response.Data = null;
                response.Message = "Đã xảy ra lỗi! Vui lòng thử lại!";
                return response;
            }
        }

        public async Task<ResponseModels<UpdateItemCategoryDTO>> UpdateAsync(int id, UpdateItemCategoryDTO itemDTO)
        {
              var response = new ResponseModels<UpdateItemCategoryDTO>
            {
                Data = null,
                Success = false
            };
            try
            {
                var itemCategory = await _unitOfWork.ItemCategoryRepository.GetByIdAsync(id);
                if(itemCategory == null)
                {
                    response.Message = "Danh mục không tồn tại";
                    return response;
                }

                if (await _unitOfWork.ItemCategoryRepository.CheckNameExisted(itemDTO.Name)) {
                    response.Message = "Tên danh mục đã tồn tại!";
                    return response;
                }

                if (await _unitOfWork.ItemCategoryRepository.CheckSlugExisted(itemDTO.Slug)) {
                    response.Message = "Slug đã tồn tại!";
                    return response;
                }

                var item = itemDTO.FromUpdateToItemCategory();
                item.ItemCategoryId = id;
                item.CreatedAt = itemCategory.CreatedAt;

                if (await _unitOfWork.ItemCategoryRepository.UpdateAsync(item) > 0)
                {
                    response.Success = true;
                    response.Message = "Cập nhật danh mục thành công!";
                    response.Data = itemDTO;
                    return response;
                }

                response.Message = "Cập nhật thất bại!";
                return response;

            }
            catch (Exception e)
            {
                response.Message = "Đã xảy ra lỗi! Vui lòng thử lại!";
                _logger.LogError(e, "Error Update");
                return response;
            }
        }

        public async Task<ResponseModels<ItemCategoryDTO>> CreateAsync(CreateItemCategoryDTO itemDTO)
        {
            var response = new ResponseModels<ItemCategoryDTO>
            {
                Data = null,
                Success = false
            };

            try
            {
                if (await _unitOfWork.ItemCategoryRepository.CheckNameExisted(itemDTO.Name))
                {
                    response.Message = "Tên danh mục đã tồn tại!";
                    return response;
                }

                if (await _unitOfWork.ItemCategoryRepository.CheckSlugExisted(itemDTO.Slug))
                {
                    response.Message = "Slug đã tồn tại!";
                    return response;
                }

                var model = itemDTO.FromCreateToItemCategory();

                var rs = await _unitOfWork.ItemCategoryRepository.CreateAsync(model);

                if (rs > 0)
                {
                    response.Success = true;
                    response.Data = model.ToItemCategoryDTO();
                    response.Message = "Thêm mới danh mục thành công!";
                    return response;
                }

                response.Message = "Thêm mới thất bại! Vui lòng thử lại";
                return response;


            }
            catch (Exception e)
            {
                response.Message = "Đã xảy ra lỗi! Vui lòng thử lại!";
                return response;
            }
        }
    }
}
