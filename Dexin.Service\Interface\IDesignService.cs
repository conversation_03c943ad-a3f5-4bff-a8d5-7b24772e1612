﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DesignView;

namespace Dexin.Service.Interface
{
    public interface IDesignService 
    {
        Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllAsync();
        Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllByUserIdAsync(int userId);
        Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllByStaffIdAsync(int staffId);

        Task<ResponseModels<IEnumerable<DesignDTO>>> GetAllByStaffAndUserAsync(int staffId, int userId);

        Task<ResponseModels<CreateDesignDTO>> CreateAsync(CreateDesignDTO designDTO);
        Task<ResponseModels<bool>> DeleteAsync(int id);
        Task<ResponseModels<UpdateDesignDTO>> UpdateAsync(int id, UpdateDesignDTO updateDesignDTO);
       
        Task<ResponseModels<DesignDTO>> GetByIdAsync(int id);

        Task<ResponseModels<Create3DDTO>> Create3DDesign(Create3DDTO designDTO);
        Task<ResponseModels<CreateDesignDTO>> Create2DDesign(CreateDesignDTO designDTO);
        Task<ResponseModels<IEnumerable<DesignDTO>>> Get3DDesigns();
        Task<ResponseModels<IEnumerable<DesignDTO>>> Get2DDesigns();

        Task<ResponseModels<bool>> Udpate3DToCustomer(int designId, string pathUrl);
        Task<ResponseModels<bool>> Update3DStatus(int designId, ThreeDStatus status);
    }
}
