﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class Color
{
    public int ColorId { get; set; }

    public string? ColorCode { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? VendorId { get; set; }

    public virtual ICollection<ProductColor> ProductColors { get; set; } = new List<ProductColor>();

    public virtual Vendor? Vendor { get; set; }
}
