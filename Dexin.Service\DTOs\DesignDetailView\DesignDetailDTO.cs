﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.DecorItemView;
using Dexin.Service.DTOs.DesignView;

namespace Dexin.Service.DTOs.DesignDetailView
{
    public class DesignDetailDTO
    {
        public int DesignDetailId { get; set; }

        public double? PositionX { get; set; }

        public double? PositionY { get; set; }

        public double? Width { get; set; }

        public double? Height { get; set; }

        public double? Rotation { get; set; }

        public int? Layer { get; set; }

        public int? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public int? DesignId { get; set; }

        public int? DecorItemId { get; set; }

        public DecorItemDTO DecorItem { get; set; }

        //public DesignDTO Design { get; set; }
    }
}
