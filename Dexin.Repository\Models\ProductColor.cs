﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class ProductColor
{
    public int ProductColorId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ProductId { get; set; }

    public int? ColorId { get; set; }

    public virtual Color? Color { get; set; }

    public virtual ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();

    public virtual Product? Product { get; set; }
}
