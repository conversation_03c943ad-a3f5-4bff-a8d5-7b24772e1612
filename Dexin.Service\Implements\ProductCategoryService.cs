﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Interface;
using Dexin.Repository.Models;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.ProductCategoryView;
using Dexin.Service.Interface;
using Dexin.Service.Mappers;
using Microsoft.Extensions.Logging;

namespace Dexin.Service.Implements
{
    public class ProductCategoryService : IProductCategoryService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IClaimsService _claimsService;
        private readonly ICurrentTime _currentTime;
        private readonly ILogger<ProductCategory> _logger;

        public ProductCategoryService(IUnitOfWork unitOfWork, IClaimsService claimsService, ICurrentTime currentTime, ILogger<ProductCategory> logger)
        {
            _unitOfWork = unitOfWork;
            _claimsService = claimsService;
            _currentTime = currentTime;
            _logger = logger;
        }

        public async Task<ResponseModels<CreateProductCategoryDTO>> CreateAsync(CreateProductCategoryDTO productCategoryDTO)
        {
            var response = new ResponseModels<CreateProductCategoryDTO>()
            {
                Success = false,
                Data = null
            };

            try
            {
                if (!await _unitOfWork.ProductCategoryRepository.CheckNameExisted(productCategoryDTO.Name))
                {
                    var model = productCategoryDTO.FromCreateToProductCategory();
                    model.CreatedAt = _currentTime.GetCurrentTime();
                    model.UserAccountId = _claimsService.GetCurrentUserId;

                    var rs = await _unitOfWork.ProductCategoryRepository.CreateAsync(model);
                    if (rs > 0)
                    {
                        response.Success = true;
                        response.Message = "Tạo mới thành công!";
                        response.Data = productCategoryDTO;
                    }
                    else
                    {
                        response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                    }
                }
                else
                {
                    response.Message = "Tên loại hàng bị trùng! Vui lòng thử tên khác!";
                }

            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
                _logger.LogError(ex, "Create Error: ");
                return response;
            }
            response.Message = "Đã có lỗi xảy ra! Vui lòng thử lại!";
            return response;
        }

        public Task<ResponseModels<bool>> DeleteAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<ResponseModels<IEnumerable<ProductCategoryDTO>>> GetAllAsync()
        {
            throw new NotImplementedException();
        }

        public Task<ResponseModels<ProductCategoryDTO>> GetByIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<ResponseModels<UpdateProductCategoryDTO>> UpdateAsync(UpdateProductCategoryDTO productCategoryDTO)
        {
            throw new NotImplementedException();
        }
    }
}
