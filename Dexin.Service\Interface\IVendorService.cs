﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.VendorView;

namespace Dexin.Service.Interface
{
    public interface IVendorService
    {
        Task<ResponseModels<IEnumerable<VendorDTO>>> GetAllAsync();
        Task<ResponseModels<VendorDTO>> GetByIdAsync(int id);
        Task<ResponseModels<CreateVendorDTO>> CreateAsync(CreateVendorDTO vendorDTO);
        Task<ResponseModels<VendorDTO>> UpdateAsync(UpdateVendorDTO vendorDTO);
        Task<ResponseModels<bool>> DeleteAsync(int id);
       
    }
}
