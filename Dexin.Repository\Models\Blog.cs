﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class Blog
{
    public int BlogPostId { get; set; }

    public string? Title { get; set; }

    public string? Subtitle { get; set; }

    public string? ThumbnailUrl { get; set; }

    public string? Content { get; set; }

    public int? Likes { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? UserAccountId { get; set; }

    public int? Views { get; set; }

    public string? AspectRatio { get; set; }

    public virtual ICollection<BlogImage> BlogImages { get; set; } = new List<BlogImage>();

    public virtual ICollection<BlogLike> BlogLikes { get; set; } = new List<BlogLike>();

    public virtual ICollection<BlogPostTag> BlogPostTags { get; set; } = new List<BlogPostTag>();

    public virtual SystemUserAccount? UserAccount { get; set; }
}
