﻿using System;
using System.Collections.Generic;
using Dexin.Repository.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Dexin.Repository.DBContext;

public partial class DexinContext : DbContext
{
    public DexinContext()
    {
    }

    public DexinContext(DbContextOptions<DexinContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Blog> Blogs { get; set; }

    public virtual DbSet<BlogImage> BlogImages { get; set; }

    public virtual DbSet<BlogLike> BlogLikes { get; set; }

    public virtual DbSet<BlogPostTag> BlogPostTags { get; set; }

    public virtual DbSet<BlogTag> BlogTags { get; set; }

    public virtual DbSet<Color> Colors { get; set; }

    public virtual DbSet<CommunityComment> CommunityComments { get; set; }

    public virtual DbSet<CommunityLike> CommunityLikes { get; set; }

    public virtual DbSet<CommunityPost> CommunityPosts { get; set; }

    public virtual DbSet<CommunityPostTag> CommunityPostTags { get; set; }

    public virtual DbSet<CommunityTag> CommunityTags { get; set; }

    public virtual DbSet<DecorItem> DecorItems { get; set; }

    public virtual DbSet<Design> Designs { get; set; }

    public virtual DbSet<DesignDetail> DesignDetails { get; set; }

    public virtual DbSet<DesignPayment> DesignPayments { get; set; }

    public virtual DbSet<FavoriteProduct> FavoriteProducts { get; set; }

    public virtual DbSet<ItemCategory> ItemCategories { get; set; }

    public virtual DbSet<Order> Orders { get; set; }

    public virtual DbSet<OrderAddress> OrderAddresses { get; set; }

    public virtual DbSet<OrderDetail> OrderDetails { get; set; }

    public virtual DbSet<Product> Products { get; set; }

    public virtual DbSet<ProductCategory> ProductCategories { get; set; }

    public virtual DbSet<ProductColor> ProductColors { get; set; }

    public virtual DbSet<ProductImage> ProductImages { get; set; }

    public virtual DbSet<ProductPayment> ProductPayments { get; set; }

    public virtual DbSet<Review> Reviews { get; set; }

    public virtual DbSet<Room> Rooms { get; set; }

    public virtual DbSet<StaffFeedback> StaffFeedbacks { get; set; }

    public virtual DbSet<SystemUserAccount> SystemUserAccounts { get; set; }

    public virtual DbSet<Vendor> Vendors { get; set; }

    public static string GetConnectionString(string connectionStringName)
    {
        var config = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json")
            .Build();

        string connectionString = config.GetConnectionString(connectionStringName);
        return connectionString;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseSqlServer(GetConnectionString("DefaultConnection")).UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);


    //    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    //#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
    //        => optionsBuilder.UseSqlServer("Data Source=localhost;Initial Catalog=DEXIN;User ID=sa;Password=*********;Integrated Security=True;Encrypt=True;Trust Server Certificate=True");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Blog>(entity =>
        {
            entity.HasKey(e => e.BlogPostId).HasName("PK__Blog__321741498D665FCC");

            entity.ToTable("Blog");

            entity.Property(e => e.BlogPostId).HasColumnName("BlogPostID");
            entity.Property(e => e.AspectRatio).HasMaxLength(255);
            entity.Property(e => e.Content).HasColumnType("text");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Subtitle).HasMaxLength(250);
            entity.Property(e => e.ThumbnailUrl)
                .IsUnicode(false)
                .HasColumnName("ThumbnailURL");
            entity.Property(e => e.Title).HasMaxLength(250);
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.Blogs)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Blog__UserAccoun__6D0D32F4");
        });

        modelBuilder.Entity<Blog>()
            .HasMany(b => b.BlogPostTags)
            .WithOne()
            .HasForeignKey(pt => pt.BlogPostId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Blog>()
                    .HasMany(b => b.BlogImages)
                    .WithOne()
                    .HasForeignKey(img => img.BlogPostId)
                    .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<BlogImage>(entity =>
        {
            entity.HasKey(e => e.ImageId).HasName("PK__BlogImag__7516F4ECCB2099BF");

            entity.ToTable("BlogImage");

            entity.Property(e => e.ImageId).HasColumnName("ImageID");
            entity.Property(e => e.BlogPostId).HasColumnName("BlogPostID");
            entity.Property(e => e.ImageUrl)
                .IsUnicode(false)
                .HasColumnName("ImageURL");

            entity.HasOne(d => d.BlogPost).WithMany(p => p.BlogImages)
                .HasForeignKey(d => d.BlogPostId)
                .HasConstraintName("FK__BlogImage__BlogP__6E01572D");
        });

        modelBuilder.Entity<BlogLike>(entity =>
        {
            entity.HasKey(e => e.BlogLikeId).HasName("PK__BlogLike__03EF897A30425110");

            entity.ToTable("BlogLike");

            entity.Property(e => e.BlogLikeId).HasColumnName("BlogLikeID");
            entity.Property(e => e.BlogPostId).HasColumnName("BlogPostID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.BlogPost).WithMany(p => p.BlogLikes)
                .HasForeignKey(d => d.BlogPostId)
                .HasConstraintName("FK__BlogLike__BlogPo__6EF57B66");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.BlogLikes)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__BlogLike__UserAc__6FE99F9F");
        });

        modelBuilder.Entity<BlogPostTag>(entity =>
        {
            entity.HasKey(e => e.BlogPostTagId).HasName("PK__BlogPost__030AD37C5C1FF0B5");

            entity.ToTable("BlogPostTag");

            entity.Property(e => e.BlogPostTagId).HasColumnName("BlogPostTagID");
            entity.Property(e => e.BlogPostId).HasColumnName("BlogPostID");
            entity.Property(e => e.BlogTagId).HasColumnName("BlogTagID");

            entity.HasOne(d => d.BlogPost).WithMany(p => p.BlogPostTags)
                .HasForeignKey(d => d.BlogPostId)
                .HasConstraintName("FK__BlogPostT__BlogP__70DDC3D8");

            entity.HasOne(d => d.BlogTag).WithMany(p => p.BlogPostTags)
                .HasForeignKey(d => d.BlogTagId)
                .HasConstraintName("FK__BlogPostT__BlogT__71D1E811");
        });

        modelBuilder.Entity<BlogTag>(entity =>
        {
            entity.HasKey(e => e.BlogTagId).HasName("PK__BlogTag__A8B8C4D3FE2B884C");

            entity.ToTable("BlogTag");

            entity.Property(e => e.BlogTagId).HasColumnName("BlogTagID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(250);
        });

        modelBuilder.Entity<Color>(entity =>
        {
            entity.HasKey(e => e.ColorId).HasName("PK__Color__8DA7676DF5960219");

            entity.ToTable("Color");

            entity.Property(e => e.ColorId).HasColumnName("ColorID");
            entity.Property(e => e.ColorCode)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.VendorId).HasColumnName("VendorID");

            entity.HasOne(d => d.Vendor).WithMany(p => p.Colors)
                .HasForeignKey(d => d.VendorId)
                .HasConstraintName("FK__Color__VendorID__72C60C4A");
        });

        modelBuilder.Entity<CommunityComment>(entity =>
        {
            entity.HasKey(e => e.CommunityCommentId).HasName("PK__Communit__43F6679DC6BCC306");

            entity.ToTable("CommunityComment");

            entity.Property(e => e.CommunityCommentId).HasColumnName("CommunityCommentID");
            entity.Property(e => e.CommunityPostId).HasColumnName("CommunityPostID");
            entity.Property(e => e.Content).HasColumnType("text");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.CommunityPost).WithMany(p => p.CommunityComments)
                .HasForeignKey(d => d.CommunityPostId)
                .HasConstraintName("FK__Community__Commu__73BA3083");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.CommunityComments)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Community__UserA__74AE54BC");
        });

        modelBuilder.Entity<CommunityLike>(entity =>
        {
            entity.HasKey(e => e.CommunityLikeId).HasName("PK__Communit__82AC6572CBD9D9A0");

            entity.ToTable("CommunityLike");

            entity.Property(e => e.CommunityLikeId).HasColumnName("CommunityLikeID");
            entity.Property(e => e.CommunityPostId).HasColumnName("CommunityPostID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.CommunityPost).WithMany(p => p.CommunityLikes)
                .HasForeignKey(d => d.CommunityPostId)
                .HasConstraintName("FK__Community__Commu__75A278F5");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.CommunityLikes)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Community__UserA__76969D2E");
        });

        modelBuilder.Entity<CommunityPost>(entity =>
        {
            entity.HasKey(e => e.CommunityPostId).HasName("PK__Communit__9A60255EC6BD4B0A");

            entity.ToTable("CommunityPost");

            entity.Property(e => e.CommunityPostId).HasColumnName("CommunityPostID");
            entity.Property(e => e.Content).HasColumnType("text");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Title).HasMaxLength(250);
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.CommunityPosts)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Community__UserA__778AC167");
        });

        modelBuilder.Entity<CommunityPostTag>(entity =>
        {
            entity.HasKey(e => e.CommunityPostTagId).HasName("PK__Communit__813942F82223255A");

            entity.ToTable("CommunityPostTag");

            entity.Property(e => e.CommunityPostTagId).HasColumnName("CommunityPostTagID");
            entity.Property(e => e.CommunityPostId).HasColumnName("CommunityPostID");
            entity.Property(e => e.CommunityTagId).HasColumnName("CommunityTagID");

            entity.HasOne(d => d.CommunityPost).WithMany(p => p.CommunityPostTags)
                .HasForeignKey(d => d.CommunityPostId)
                .HasConstraintName("FK__Community__Commu__787EE5A0");

            entity.HasOne(d => d.CommunityTag).WithMany(p => p.CommunityPostTags)
                .HasForeignKey(d => d.CommunityTagId)
                .HasConstraintName("FK__Community__Commu__797309D9");
        });

        modelBuilder.Entity<CommunityTag>(entity =>
        {
            entity.HasKey(e => e.CommunityTagId).HasName("PK__Communit__2B73ADDEBB07763B");

            entity.ToTable("CommunityTag");

            entity.Property(e => e.CommunityTagId).HasColumnName("CommunityTagID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(250);
        });

        modelBuilder.Entity<DecorItem>(entity =>
        {
            entity.HasKey(e => e.DecorItemId).HasName("PK__DecorIte__8FF43DEC531B99A5");

            entity.ToTable("DecorItem");

            entity.Property(e => e.DecorItemId).HasColumnName("DecorItemID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ImageUrl)
                .IsUnicode(false)
                .HasColumnName("ImageURL");
            entity.Property(e => e.ItemCategoryId).HasColumnName("ItemCategoryID");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.OriginHeight).HasColumnName("Origin_Height");
            entity.Property(e => e.OriginWidth).HasColumnName("Origin_Width");

            entity.HasOne(d => d.ItemCategory).WithMany(p => p.DecorItems)
                .HasForeignKey(d => d.ItemCategoryId)
                .HasConstraintName("FK__DecorItem__ItemC__7A672E12");
        });

        modelBuilder.Entity<Design>(entity =>
        {
            entity.HasKey(e => e.DesignId).HasName("PK__Design__32B8E17FC909D589");

            entity.ToTable("Design");

            entity.Property(e => e.DesignId).HasColumnName("DesignID");
            entity.Property(e => e.CanvasPosX).HasColumnName("Canvas_PosX");
            entity.Property(e => e.CanvasPosY).HasColumnName("Canvas_PosY");
            entity.Property(e => e.CanvasScale).HasColumnName("Canvas_Scale");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.PathUrl)
                .IsUnicode(false)
                .HasColumnName("PathURL");
            entity.Property(e => e.ThumbnailUrl)
                .IsUnicode(false)
                .HasColumnName("ThumbnailURL");
            entity.Property(e => e.Title).HasMaxLength(250);

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.DesignCreatedByNavigations)
                .HasForeignKey(d => d.CreatedBy)
                .HasConstraintName("FK__Design__CreatedB__7B5B524B");

            entity.HasOne(d => d.Staff).WithMany(p => p.DesignStaffs)
                .HasForeignKey(d => d.StaffId)
                .HasConstraintName("FK__Design__StaffId__7C4F7684");
        });

        modelBuilder.Entity<DesignDetail>(entity =>
        {
            entity.HasKey(e => e.DesignDetailId).HasName("PK__DesignDe__CBFA7AAA6850B037");

            entity.ToTable("DesignDetail");

            entity.Property(e => e.DesignDetailId).HasColumnName("DesignDetailID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.DecorItemId).HasColumnName("DecorItemID");
            entity.Property(e => e.DesignId).HasColumnName("DesignID");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");

            entity.HasOne(d => d.DecorItem).WithMany(p => p.DesignDetails)
                .HasForeignKey(d => d.DecorItemId)
                .HasConstraintName("FK__DesignDet__Decor__7D439ABD");

            entity.HasOne(d => d.Design).WithMany(p => p.DesignDetails)
                .HasForeignKey(d => d.DesignId)
                .HasConstraintName("FK__DesignDet__Desig__7E37BEF6");
        });

        modelBuilder.Entity<DesignPayment>(entity =>
        {
            entity.HasKey(e => e.DesignPaymentId).HasName("PK__DesignPa__7424904524CDC642");

            entity.ToTable("DesignPayment");

            entity.Property(e => e.DesignPaymentId).HasColumnName("DesignPaymentID");
            entity.Property(e => e.Amount).HasColumnType("decimal(12, 2)");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.CurrencyCode)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.DesignId).HasColumnName("DesignID");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.PaymentDate).HasColumnType("datetime");
            entity.Property(e => e.PaymentMethod).HasMaxLength(100);
            entity.Property(e => e.PaymentProvide).HasMaxLength(250);
            entity.Property(e => e.Status).HasMaxLength(250);
            entity.Property(e => e.TransactionIdGateway)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("Transaction_ID_Gateway");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.Design).WithMany(p => p.DesignPayments)
                .HasForeignKey(d => d.DesignId)
                .HasConstraintName("FK__DesignPay__Desig__7F2BE32F");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.DesignPayments)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__DesignPay__UserA__00200768");
        });

        modelBuilder.Entity<FavoriteProduct>(entity =>
        {
            entity.HasKey(e => e.FavoriteProductId).HasName("PK__Favorite__1245364EB6C22C0A");

            entity.ToTable("FavoriteProduct");

            entity.Property(e => e.FavoriteProductId).HasColumnName("FavoriteProductID");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.Product).WithMany(p => p.FavoriteProducts)
                .HasForeignKey(d => d.ProductId)
                .HasConstraintName("FK__FavoriteP__Produ__01142BA1");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.FavoriteProducts)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__FavoriteP__UserA__02084FDA");
        });

        modelBuilder.Entity<ItemCategory>(entity =>
        {
            entity.HasKey(e => e.ItemCategoryId).HasName("PK__ItemCate__C24A29056A92EF75");

            entity.ToTable("ItemCategory");

            entity.Property(e => e.ItemCategoryId).HasColumnName("ItemCategoryID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Slug).HasMaxLength(100);
        });

        modelBuilder.Entity<Order>(entity =>
        {
            entity.HasKey(e => e.OrdersId).HasName("PK__Orders__630B9956EA38DE2E");

            entity.Property(e => e.OrdersId).HasColumnName("OrdersID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.OrderAddressId).HasColumnName("OrderAddressID");
            entity.Property(e => e.OrderCode)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.PaymentMethod).HasMaxLength(50);
            entity.Property(e => e.ShippingAmount).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Subtotal).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.OrderAddress).WithMany(p => p.Orders)
                .HasForeignKey(d => d.OrderAddressId)
                .HasConstraintName("FK__Orders__OrderAdd__06CD04F7");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.Orders)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Orders__UserAcco__07C12930");
        });

        modelBuilder.Entity<OrderAddress>(entity =>
        {
            entity.HasKey(e => e.OrderAddressId).HasName("PK__OrderAdd__34B754C56736BE4B");

            entity.ToTable("OrderAddress");

            entity.Property(e => e.OrderAddressId).HasColumnName("OrderAddressID");
            entity.Property(e => e.AddressLine).HasMaxLength(250);
            entity.Property(e => e.City).HasMaxLength(250);
            entity.Property(e => e.Country).HasMaxLength(250);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.District).HasMaxLength(250);
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");
            entity.Property(e => e.Ward).HasMaxLength(250);

            entity.HasOne(d => d.UserAccount).WithMany(p => p.OrderAddresses)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__OrderAddr__UserA__02FC7413");
        });

        modelBuilder.Entity<OrderDetail>(entity =>
        {
            entity.HasKey(e => e.OrderDetailId).HasName("PK__OrderDet__D3B9D30CF372B94B");

            entity.ToTable("OrderDetail");

            entity.Property(e => e.OrderDetailId).HasColumnName("OrderDetailID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.OrdersId).HasColumnName("OrdersID");
            entity.Property(e => e.ProductColorId).HasColumnName("ProductColorID");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");
            entity.Property(e => e.TotalPrice).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(10, 2)");

            entity.HasOne(d => d.Orders).WithMany(p => p.OrderDetails)
                .HasForeignKey(d => d.OrdersId)
                .HasConstraintName("FK__OrderDeta__Order__03F0984C");

            entity.HasOne(d => d.ProductColor).WithMany(p => p.OrderDetails)
                .HasForeignKey(d => d.ProductColorId)
                .HasConstraintName("FK__OrderDeta__Produ__05D8E0BE");

            entity.HasOne(d => d.Product).WithMany(p => p.OrderDetails)
                .HasForeignKey(d => d.ProductId)
                .HasConstraintName("FK__OrderDeta__Produ__04E4BC85");
        });

        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.ProductId).HasName("PK__Product__B40CC6EDDAA946E0");

            entity.ToTable("Product");

            entity.Property(e => e.ProductId).HasColumnName("ProductID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(250);
            entity.Property(e => e.Price).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ProductCategoryId).HasColumnName("ProductCategoryID");
            entity.Property(e => e.Sku)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.VendorId).HasColumnName("VendorID");

            entity.HasOne(d => d.ProductCategory).WithMany(p => p.Products)
                .HasForeignKey(d => d.ProductCategoryId)
                .HasConstraintName("FK__Product__Product__08B54D69");

            entity.HasOne(d => d.Vendor).WithMany(p => p.Products)
                .HasForeignKey(d => d.VendorId)
                .HasConstraintName("FK__Product__VendorI__09A971A2");
        });

        modelBuilder.Entity<ProductCategory>(entity =>
        {
            entity.HasKey(e => e.ProductCategoryId).HasName("PK__ProductC__3224ECEE96A6B16A");

            entity.ToTable("ProductCategory");

            entity.Property(e => e.ProductCategoryId).HasColumnName("ProductCategoryID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(250);
            entity.Property(e => e.ParentId).HasColumnName("ParentID");
            entity.Property(e => e.Status).HasColumnName("status");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.Parent).WithMany(p => p.InverseParent)
                .HasForeignKey(d => d.ParentId)
                .HasConstraintName("FK__ProductCa__Paren__0A9D95DB");
        });

        modelBuilder.Entity<ProductColor>(entity =>
        {
            entity.HasKey(e => e.ProductColorId).HasName("PK__ProductC__C5DB681E352202AF");

            entity.ToTable("ProductColor");

            entity.Property(e => e.ProductColorId).HasColumnName("ProductColorID");
            entity.Property(e => e.ColorId).HasColumnName("ColorID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");

            entity.HasOne(d => d.Color).WithMany(p => p.ProductColors)
                .HasForeignKey(d => d.ColorId)
                .HasConstraintName("FK__ProductCo__Color__0B91BA14");

            entity.HasOne(d => d.Product).WithMany(p => p.ProductColors)
                .HasForeignKey(d => d.ProductId)
                .HasConstraintName("FK__ProductCo__Produ__0C85DE4D");
        });

        modelBuilder.Entity<ProductImage>(entity =>
        {
            entity.HasKey(e => e.ProductImageId).HasName("PK__ProductI__07B2B1D89C057AE2");

            entity.ToTable("ProductImage");

            entity.Property(e => e.ProductImageId).HasColumnName("ProductImageID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ImageUrl)
                .IsUnicode(false)
                .HasColumnName("ImageURL");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");

            entity.HasOne(d => d.Product).WithMany(p => p.ProductImages)
                .HasForeignKey(d => d.ProductId)
                .HasConstraintName("FK__ProductIm__Produ__0D7A0286");
        });

        modelBuilder.Entity<ProductPayment>(entity =>
        {
            entity.HasKey(e => e.ProductPaymentId).HasName("PK__ProductP__0DF9753E75449450");

            entity.ToTable("ProductPayment");

            entity.Property(e => e.ProductPaymentId).HasColumnName("ProductPaymentID");
            entity.Property(e => e.Amount).HasColumnType("decimal(12, 2)");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.CurrencyCode)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.OrdersId).HasColumnName("OrdersID");
            entity.Property(e => e.PaymentDate).HasColumnType("datetime");
            entity.Property(e => e.PaymentMethod).HasMaxLength(100);
            entity.Property(e => e.PaymentProvide).HasMaxLength(250);
            entity.Property(e => e.Status).HasMaxLength(250);
            entity.Property(e => e.TransactionIdGateway)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("Transaction_ID_Gateway");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.Orders).WithMany(p => p.ProductPayments)
                .HasForeignKey(d => d.OrdersId)
                .HasConstraintName("FK__ProductPa__Order__0E6E26BF");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.ProductPayments)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__ProductPa__UserA__0F624AF8");
        });

        modelBuilder.Entity<Review>(entity =>
        {
            entity.HasKey(e => e.ReviewId).HasName("PK__Review__74BC79AE532F56E3");

            entity.ToTable("Review");

            entity.Property(e => e.ReviewId).HasColumnName("ReviewID");
            entity.Property(e => e.Comment).HasColumnType("text");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.OrderDetailId).HasColumnName("OrderDetailID");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");
            entity.Property(e => e.Title).HasMaxLength(250);
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.OrderDetail).WithMany(p => p.Reviews)
                .HasForeignKey(d => d.OrderDetailId)
                .HasConstraintName("FK__Review__OrderDet__10566F31");

            entity.HasOne(d => d.Product).WithMany(p => p.Reviews)
                .HasForeignKey(d => d.ProductId)
                .HasConstraintName("FK__Review__ProductI__114A936A");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.Reviews)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Review__UserAcco__123EB7A3");
        });

        modelBuilder.Entity<Room>(entity =>
        {
            entity.ToTable("Room");

            entity.HasIndex(e => e.StaffId, "IX_Room_StaffID");

            entity.HasIndex(e => e.UserId, "IX_Room_UserID");

            entity.Property(e => e.RoomId)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("RoomID");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.StaffId).HasColumnName("StaffID");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Staff).WithMany(p => p.RoomStaffs)
                .HasForeignKey(d => d.StaffId)
                .HasConstraintName("FK__Room__StaffID__18EBB532");

            entity.HasOne(d => d.User).WithMany(p => p.RoomUsers)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK__Room__UserID__17F790F9");
        });


        modelBuilder.Entity<StaffFeedback>(entity =>
        {
            entity.HasKey(e => e.StaffFeedbackId).HasName("PK__StaffFee__BE232C63FDE6B46E");

            entity.ToTable("StaffFeedback");

            entity.Property(e => e.StaffFeedbackId).HasColumnName("StaffFeedbackID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.StaffId).HasColumnName("StaffID");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.Staff).WithMany(p => p.StaffFeedbackStaffs)
                .HasForeignKey(d => d.StaffId)
                .HasConstraintName("FK__StaffFeed__Staff__1332DBDC");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.StaffFeedbackUserAccounts)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__StaffFeed__UserA__14270015");
        });

        modelBuilder.Entity<SystemUserAccount>(entity =>
        {
            entity.HasKey(e => e.UserAccountId);

            entity.ToTable("System.UserAccount");

            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");
            entity.Property(e => e.Avartar).IsUnicode(false);
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(150);
            entity.Property(e => e.EmployeeCode).HasMaxLength(50);
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.ModifiedBy).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Password).HasMaxLength(100);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
        });

        modelBuilder.Entity<Vendor>(entity =>
        {
            entity.HasKey(e => e.VendorId).HasName("PK__Vendor__FC8618D3F477195E");

            entity.ToTable("Vendor");

            entity.Property(e => e.VendorId).HasColumnName("VendorID");
            entity.Property(e => e.ContactEmail)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.LogoUrl)
                .IsUnicode(false)
                .HasColumnName("LogoURL");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.TaxId)
                .IsUnicode(false)
                .HasColumnName("TaxID");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.Vendors)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Vendor__UserAcco__151B244E");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
