﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Enums;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.ItemCategoryView;

namespace Dexin.Service.Mappers
{
    public static class ItemCategoryMappers
    {
        public static ItemCategoryDTO ToItemCategoryDTO(this ItemCategory itemCategory)
        {
            return new ItemCategoryDTO
            {
                
                CreatedAt = itemCategory.CreatedAt,
                Description = itemCategory.Description,
                ModifiedAt = itemCategory.ModifiedAt,
                Name = itemCategory.Name,
                Slug = itemCategory.Slug,
                Status = itemCategory.Status,
                ItemCategoryId = itemCategory.ItemCategoryId,

            };
        }

        public static ItemCategory FromCreateToItemCategory(this CreateItemCategoryDTO itemCategoryDTO)
        {

            return new ItemCategory
            {
                CreatedAt = DateTime.Now,
                Description = itemCategoryDTO.Description,
                Name = itemCategoryDTO.Name.Trim(),
                Slug = itemCategoryDTO?.Slug.ToLower().Trim(),
                Status = (int)Status.Active,

            };
        }

        public static ItemCategory FromUpdateToItemCategory(this UpdateItemCategoryDTO itemCategoryDTO)
        {
            return new ItemCategory
            {
                Status = itemCategoryDTO.Status,
                ModifiedAt = DateTime.Now,
                Name = itemCategoryDTO.Name,
                Slug = itemCategoryDTO.Slug,
                Description = itemCategoryDTO.Description,
            };
        }
    }
}
