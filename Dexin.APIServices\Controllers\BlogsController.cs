﻿using Dexin.Repository.Enums;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.BlogView;
using Dexin.Service.Implements;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlogsController : ControllerBase
    {
        private readonly IBlogService _blogService;
        private readonly IImageService _imageService;

        public BlogsController(IBlogService blogService, IImageService imageService)
        {
            _blogService = blogService;
            _imageService = imageService;
        }

        // POST: api/Blogs
        [HttpPost]
        [Authorize(Roles = "User")]
        public async Task<IActionResult> CreateBlog([FromForm] CreateBlogWithImageRequest request)
        {
            var thumbNail = await _imageService.UploadImageAsync(request.ThumbnailUrl);

            List<string> images = await _imageService.UploadImagesAsync(request.Images);

            var userName = User.FindFirstValue(ClaimTypes.Name);
            var userId = User.FindFirstValue("UserId");

            var dto = new CreateBlogDTO
            {
                AspectRatio = request.AspectRatio,
                Content = request.Content,
                Image = images,
                Status = (int)Status.Active,
                Subtitle = request.Title,
                TagIds = request.TagIds,
                ThumbnailUrl = thumbNail,
                Title = request.Title,
                UserAccountId = int.Parse(userId),

            };
            var result = await _blogService.CreateAsync(dto, userName);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        // GET: api/Blogs
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var result = await _blogService.GetAllAync();
            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "User, Manager")]
        public async Task<ActionResult<ResponseModels<bool>>> DeleteAsync(int id)
        {
            var response = await _blogService.DeleteAsync(id);
            if (!response.Success)
            {
                return NotFound(response); // Handle not found cases appropriately
            }

            return Ok(response);
        }

        [HttpPut]
        [Authorize(Roles = "User")]
        public async Task<IActionResult> UpdateBlogAsync(int id, [FromForm] UpdateBlogWithImageRequest request)
        {
            var thumbNail = "";
            List<string> images = new List<string>();

            if (request.ThumbnailUrl != null)
            {
                thumbNail = await _imageService.UploadImageAsync(request.ThumbnailUrl);
            }

            if (request.Image != null)
            {
                images = await _imageService.UploadImagesAsync(request.Image);
            }
            var dto = new UpdateBlogDTO
            {
                ThumbnailUrl = thumbNail,
                Image = images,
                Content = request.Content,
                Status = request.Status,
                Subtitle = request.Title,
                TagIds = request.TagIds,
                Title = request.Title,
            };


            var result = await _blogService.UpdateAsync(id, dto);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
    }
}
