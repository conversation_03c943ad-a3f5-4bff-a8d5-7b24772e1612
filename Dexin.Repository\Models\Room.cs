﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class Room
{
    public string RoomId { get; set; } = null!;

    public int? UserId { get; set; }

    public int? StaffId { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public virtual SystemUserAccount? Staff { get; set; }

    public virtual SystemUserAccount? User { get; set; }
}
