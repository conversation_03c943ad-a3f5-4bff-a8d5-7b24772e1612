﻿using System;
using System.Collections.Generic;

namespace Dexin.Repository.Models;

public partial class CommunityTag
{
    public int CommunityTagId { get; set; }

    public string? Name { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public virtual ICollection<CommunityPostTag> CommunityPostTags { get; set; } = new List<CommunityPostTag>();
}
