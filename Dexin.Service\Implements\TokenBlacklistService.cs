﻿using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Service.Interface;
using Microsoft.Extensions.Caching.Distributed;

namespace Dexin.Service.Implements
{
    public class TokenBlacklistService : ITokenBlacklistService
    {
        private readonly IDistributedCache _cache;
        private readonly JwtSecurityTokenHandler _tokenHandler;

        public TokenBlacklistService(IDistributedCache cache)
        {
            _cache = cache;
            _tokenHandler = new JwtSecurityTokenHandler();
        }

        public async Task AddToBlacklistAsync(string token, DateTime expiration)
        {
            // Calculate remaining lifetime
            var remainingLifetime = expiration - DateTime.UtcNow;

            if (remainingLifetime > TimeSpan.Zero)
            {
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = remainingLifetime
                };
                // Store the token string itself, or a hash of it, in the cache
                // The value stored (e.g., "blacklisted") doesn't matter as much as the key's presence
                await _cache.SetStringAsync(GetBlacklistKey(token), "blacklisted", options);
            }
        }

        public async Task<bool> IsBlacklistedAsync(string token)
        {
            var cachedToken = await _cache.GetStringAsync(GetBlacklistKey(token));
            return !string.IsNullOrEmpty(cachedToken);
        }

        private string GetBlacklistKey(string token)
        {
            // Using a prefix to avoid key collisions
            return $"blacklist:{token}";
        }
    }
}
