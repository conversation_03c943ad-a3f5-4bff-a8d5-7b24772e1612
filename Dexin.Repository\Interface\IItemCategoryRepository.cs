﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IItemCategoryRepository : IGenericRepository<ItemCategory>
    {
        Task<bool> CheckNameExisted(string name);   
        Task<bool> CheckSlugExisted(string slug);
        Task<bool> CheckIdExisted(int id);
    }
}
