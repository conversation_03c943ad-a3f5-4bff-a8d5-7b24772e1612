﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;

namespace Dexin.Repository.Interface
{
    public interface IRoomRepository : IGenericRepository<Room>
    { 
        Task<IEnumerable<Room>> GetAllIncludeAsync();
        Task<IEnumerable<Room>> GetAllByCustomerIdIncludeAsync(int customerId);
        Task<IEnumerable<Room>> GetAllByStaffIdIncludeAsync(int staffId);
        Task<bool> ChekIdExisted(string id);
        Task<Room> GetByIdIncludeAsync(string id);  
    }
}
