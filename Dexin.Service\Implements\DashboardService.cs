using Dexin.Repository.Enums;
using Dexin.Repository.Interface;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DashboardView;
using Dexin.Service.Interface;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dexin.Service.Implements
{
    public class DashboardService : IDashboardService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DashboardService> _logger;

        public DashboardService(IUnitOfWork unitOfWork, ILogger<DashboardService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<ResponseModels<DashboardOverviewDTO>> GetDashboardOverviewAsync()
        {
            var response = new ResponseModels<DashboardOverviewDTO>
            {
                Success = false,
                Data = null
            };

            try
            {
                var totalUsers = await _unitOfWork.SystemUserAccountRepository.GetTotalUsersCountAsync();
                var totalOrders = await _unitOfWork.OrderRepository.GetTotalOrdersCountAsync();
                var totalRevenue = await _unitOfWork.OrderRepository.GetTotalRevenueAsync() + 
                                  await _unitOfWork.DesignPaymentRepository.GetTotalRevenueAsync();
                var totalDesigns = await _unitOfWork.DesignRepository.GetTotalDesignsCountAsync();
                var totalBlogs = await _unitOfWork.BlogRepository.GetTotalBlogsCountAsync();
                var totalReviews = await _unitOfWork.ReviewRepository.GetTotalReviewsCountAsync();
                var averageRating = await _unitOfWork.ReviewRepository.GetAverageRatingAsync();

                response.Data = new DashboardOverviewDTO
                {
                    TotalUsers = totalUsers,
                    TotalOrders = totalOrders,
                    TotalRevenue = totalRevenue,
                    TotalDesigns = totalDesigns,
                    TotalBlogs = totalBlogs,
                    TotalReviews = totalReviews,
                    AverageRating = averageRating
                };

                response.Success = true;
                response.Message = "Lấy thống kê tổng quan thành công";
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra khi lấy thống kê tổng quan";
                _logger.LogError(ex, "GetDashboardOverviewAsync Error: ");
            }

            return response;
        }

        public async Task<ResponseModels<TransactionStatisticsDTO>> GetTransactionStatisticsAsync(string period = "month")
        {
            var response = new ResponseModels<TransactionStatisticsDTO>
            {
                Success = false,
                Data = null
            };

            try
            {
                var (startDate, endDate) = GetDateRange(period);
                var today = DateTime.Today;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var monthStart = new DateTime(today.Year, today.Month, 1);

                // Revenue Statistics
                var totalRevenue = await _unitOfWork.OrderRepository.GetTotalRevenueAsync() + 
                                  await _unitOfWork.DesignPaymentRepository.GetTotalRevenueAsync();
                var revenueThisMonth = await _unitOfWork.OrderRepository.GetRevenueByDateRangeAsync(monthStart, today.AddDays(1)) +
                                      await _unitOfWork.DesignPaymentRepository.GetRevenueByDateRangeAsync(monthStart, today.AddDays(1));
                var revenueThisWeek = await _unitOfWork.OrderRepository.GetRevenueByDateRangeAsync(weekStart, today.AddDays(1)) +
                                     await _unitOfWork.DesignPaymentRepository.GetRevenueByDateRangeAsync(weekStart, today.AddDays(1));
                var revenueToday = await _unitOfWork.OrderRepository.GetRevenueByDateRangeAsync(today, today.AddDays(1)) +
                                  await _unitOfWork.DesignPaymentRepository.GetRevenueByDateRangeAsync(today, today.AddDays(1));

                var designRevenue = await _unitOfWork.DesignPaymentRepository.GetTotalRevenueAsync();
                var productRevenue = await _unitOfWork.ProductPaymentRepository.GetTotalRevenueAsync();

                // Design Payment Statistics
                var totalDesignPayments = await _unitOfWork.DesignPaymentRepository.GetTotalPaymentsCountAsync();
                var designPaymentsThisMonth = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByDateRangeAsync(monthStart, today.AddDays(1));
                var designPaymentsThisWeek = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByDateRangeAsync(weekStart, today.AddDays(1));
                var designPaymentsToday = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByDateRangeAsync(today, today.AddDays(1));
                var totalDesignAmount = await _unitOfWork.DesignPaymentRepository.GetTotalRevenueAsync();

                // Product Payment Statistics  
                var totalProductPayments = await _unitOfWork.ProductPaymentRepository.GetTotalPaymentsCountAsync();
                var productPaymentsThisMonth = await _unitOfWork.ProductPaymentRepository.GetPaymentCountByDateRangeAsync(monthStart, today.AddDays(1));
                var productPaymentsThisWeek = await _unitOfWork.ProductPaymentRepository.GetPaymentCountByDateRangeAsync(weekStart, today.AddDays(1));
                var productPaymentsToday = await _unitOfWork.ProductPaymentRepository.GetPaymentCountByDateRangeAsync(today, today.AddDays(1));
                var totalProductAmount = await _unitOfWork.ProductPaymentRepository.GetTotalRevenueAsync();

                // Payment Status Statistics
                var successfulPayments = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByStatusAsync("PAID") +
                                         await _unitOfWork.ProductPaymentRepository.GetPaymentCountByStatusAsync("PAID");
                var pendingPayments = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByStatusAsync("PENDING") +
                                     await _unitOfWork.ProductPaymentRepository.GetPaymentCountByStatusAsync("PENDING");
                var failedPayments = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByStatusAsync("FAILED") +
                                    await _unitOfWork.ProductPaymentRepository.GetPaymentCountByStatusAsync("FAILED");
                var cancelledPayments = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByStatusAsync("CANCELLED") +
                                       await _unitOfWork.ProductPaymentRepository.GetPaymentCountByStatusAsync("CANCELLED");

                // Revenue Data for Charts
                var revenueData = await GetRevenueDataAsync(startDate, endDate);

                response.Data = new TransactionStatisticsDTO
                {
                    Revenue = new RevenueStatisticsDTO
                    {
                        TotalRevenue = totalRevenue,
                        RevenueThisMonth = revenueThisMonth,
                        RevenueThisWeek = revenueThisWeek,
                        RevenueToday = revenueToday,
                        DesignRevenue = designRevenue,
                        ProductRevenue = productRevenue
                    },
                    DesignPayments = new PaymentStatisticsDTO
                    {
                        TotalPayments = totalDesignPayments,
                        PaymentsThisMonth = designPaymentsThisMonth,
                        PaymentsThisWeek = designPaymentsThisWeek,
                        PaymentsToday = designPaymentsToday,
                        TotalAmount = totalDesignAmount
                    },
                    ProductPayments = new PaymentStatisticsDTO
                    {
                        TotalPayments = totalProductPayments,
                        PaymentsThisMonth = productPaymentsThisMonth,
                        PaymentsThisWeek = productPaymentsThisWeek,
                        PaymentsToday = productPaymentsToday,
                        TotalAmount = totalProductAmount
                    },
                    PaymentStatus = new PaymentStatusStatisticsDTO
                    {
                        SuccessfulPayments = successfulPayments,
                        PendingPayments = pendingPayments,
                        FailedPayments = failedPayments,
                        CancelledPayments = cancelledPayments
                    },
                    RevenueData = revenueData
                };

                response.Success = true;
                response.Message = "Lấy thống kê giao dịch thành công";
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra khi lấy thống kê giao dịch";
                _logger.LogError(ex, "GetTransactionStatisticsAsync Error: ");
            }

            return response;
        }

        public async Task<ResponseModels<UserStatisticsDTO>> GetUserStatisticsAsync(string period = "month")
        {
            var response = new ResponseModels<UserStatisticsDTO>
            {
                Success = false,
                Data = null
            };

            try
            {
                var today = DateTime.Today;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var monthStart = new DateTime(today.Year, today.Month, 1);

                var totalUsers = await _unitOfWork.SystemUserAccountRepository.GetTotalUsersCountAsync();
                var newUsersThisMonth = await _unitOfWork.SystemUserAccountRepository.GetNewUsersCountByDateRangeAsync(monthStart, today.AddDays(1));
                var newUsersThisWeek = await _unitOfWork.SystemUserAccountRepository.GetNewUsersCountByDateRangeAsync(weekStart, today.AddDays(1));
                var newUsersToday = await _unitOfWork.SystemUserAccountRepository.GetNewUsersCountByDateRangeAsync(today, today.AddDays(1));
                var activeUsers = await _unitOfWork.SystemUserAccountRepository.GetActiveUsersCountAsync();

                // Role Distribution
                var usersByRole = await _unitOfWork.SystemUserAccountRepository.GetUserCountByRoleAsync();
                var roleDistribution = new UserRoleDistributionDTO
                {
                    Users = usersByRole.ContainsKey(Role.User) ? usersByRole[Role.User] : 0,
                    Staff = usersByRole.ContainsKey(Role.Staff) ? usersByRole[Role.Staff] : 0,
                    Managers = usersByRole.ContainsKey(Role.Manager) ? usersByRole[Role.Manager] : 0,
                    Vendors = usersByRole.ContainsKey(Role.Vendor) ? usersByRole[Role.Vendor] : 0
                };

                // User Growth Data
                var (startDate, endDate) = GetDateRange(period);
                var userGrowthData = await GetUserGrowthDataAsync(startDate, endDate);

                // Recent Users
                var recentUsersData = await _unitOfWork.SystemUserAccountRepository.GetRecentUsersAsync(10);
                var recentUsers = recentUsersData.Select(u => new RecentUserDTO
                {
                    UserAccountId = u.UserAccountId,
                    UserName = u.UserName,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email,
                    Role = u.RoleId.ToString(),
                    CreatedDate = u.CreatedDate,
                    Avatar = u.Avartar
                }).ToList();

                response.Data = new UserStatisticsDTO
                {
                    TotalUsers = totalUsers,
                    NewUsersThisMonth = newUsersThisMonth,
                    NewUsersThisWeek = newUsersThisWeek,
                    NewUsersToday = newUsersToday,
                    ActiveUsers = activeUsers,
                    RoleDistribution = roleDistribution,
                    UserGrowthData = userGrowthData,
                    RecentUsers = recentUsers
                };

                response.Success = true;
                response.Message = "Lấy thống kê người dùng thành công";
            }
            catch (Exception ex)
            {
                response.Message = "Đã có lỗi xảy ra khi lấy thống kê người dùng";
                _logger.LogError(ex, "GetUserStatisticsAsync Error: ");
            }

            return response;
        }

        private (DateTime startDate, DateTime endDate) GetDateRange(string period)
        {
            var today = DateTime.Today;
            return period.ToLower() switch
            {
                "day" => (today, today.AddDays(1)),
                "week" => (today.AddDays(-7), today.AddDays(1)),
                "month" => (today.AddDays(-30), today.AddDays(1)),
                "year" => (today.AddDays(-365), today.AddDays(1)),
                _ => (today.AddDays(-30), today.AddDays(1))
            };
        }

        private async Task<List<RevenueDataDTO>> GetRevenueDataAsync(DateTime startDate, DateTime endDate)
        {
            var revenueData = new List<RevenueDataDTO>();
            var currentDate = startDate.Date;

            while (currentDate <= endDate.Date)
            {
                var nextDate = currentDate.AddDays(1);
                
                var designRevenue = await _unitOfWork.DesignPaymentRepository.GetRevenueByDateRangeAsync(currentDate, nextDate);
                var productRevenue = await _unitOfWork.ProductPaymentRepository.GetRevenueByDateRangeAsync(currentDate, nextDate);
                
                var designCount = await _unitOfWork.DesignPaymentRepository.GetPaymentCountByDateRangeAsync(currentDate, nextDate);
                var productCount = await _unitOfWork.ProductPaymentRepository.GetPaymentCountByDateRangeAsync(currentDate, nextDate);

                revenueData.Add(new RevenueDataDTO
                {
                    Date = currentDate,
                    DesignRevenue = designRevenue,
                    ProductRevenue = productRevenue,
                    TotalRevenue = designRevenue + productRevenue,
                    TransactionCount = designCount + productCount
                });

                currentDate = currentDate.AddDays(1);
            }

            return revenueData;
        }

        private async Task<List<UserGrowthDataDTO>> GetUserGrowthDataAsync(DateTime startDate, DateTime endDate)
        {
            var userGrowthData = new List<UserGrowthDataDTO>();
            var currentDate = startDate.Date;
            var totalUsers = await _unitOfWork.SystemUserAccountRepository.GetUsersCountBeforeDateAsync(startDate);

            while (currentDate <= endDate.Date)
            {
                var nextDate = currentDate.AddDays(1);
                var newUsers = await _unitOfWork.SystemUserAccountRepository.GetNewUsersCountByDateRangeAsync(currentDate, nextDate);
                totalUsers += newUsers;

                userGrowthData.Add(new UserGrowthDataDTO
                {
                    Date = currentDate,
                    NewUsers = newUsers,
                    TotalUsers = totalUsers
                });

                currentDate = currentDate.AddDays(1);
            }

            return userGrowthData;
        }
    }
}
