# Dashboard API Testing Guide

## API Endpoints đã tạo

### 1. Dashboard Overview
**GET** `/api/Dashboard/overview`
- **Authorization**: Manager, Staff
- **Description**: <PERSON><PERSON><PERSON> thống kê tổng quan
- **Response**: 
```json
{
  "success": true,
  "message": "<PERSON><PERSON>y thống kê tổng quan thành công",
  "data": {
    "totalUsers": 150,
    "totalOrders": 45,
    "totalRevenue": ********,
    "totalDesigns": 30,
    "totalBlogs": 12,
    "totalReviews": 89,
    "averageRating": 4.2
  }
}
```

### 2. User Statistics
**GET** `/api/Dashboard/user-statistics?period=month`
- **Authorization**: Manager, Staff
- **Parameters**: 
  - `period` (optional): day, week, month, year (default: month)
- **Description**: Thống kê người dùng theo thời gian
- **Response**:
```json
{
  "success": true,
  "message": "<PERSON><PERSON><PERSON> thống kê người dùng thành công",
  "data": {
    "totalUsers": 150,
    "newUsersThisMonth": 25,
    "newUsersThisWeek": 8,
    "newUsersToday": 2,
    "activeUsers": 120,
    "roleDistribution": {
      "users": 130,
      "staff": 15,
      "managers": 3,
      "vendors": 2
    },
    "userGrowthData": [
      {
        "date": "2024-01-01T00:00:00",
        "newUsers": 5,
        "totalUsers": 125
      }
    ],
    "recentUsers": [
      {
        "userAccountId": 151,
        "userName": "newuser123",
        "firstName": "Nguyen",
        "lastName": "Van A",
        "email": "<EMAIL>",
        "role": "User",
        "createdDate": "2024-01-15T10:30:00",
        "avatar": "https://example.com/avatar.jpg"
      }
    ]
  }
}
```

### 3. Transaction Statistics
**GET** `/api/Dashboard/transaction-statistics?period=month`
- **Authorization**: Manager, Staff
- **Parameters**: 
  - `period` (optional): day, week, month, year (default: month)
- **Description**: Thống kê giao dịch và doanh thu
- **Response**:
```json
{
  "success": true,
  "message": "Lấy thống kê giao dịch thành công",
  "data": {
    "revenue": {
      "totalRevenue": ********,
      "revenueThisMonth": 5000000,
      "revenueThisWeek": 1200000,
      "revenueToday": 150000,
      "designRevenue": ********,
      "productRevenue": ********
    },
    "designPayments": {
      "totalPayments": 30,
      "paymentsThisMonth": 8,
      "paymentsThisWeek": 2,
      "paymentsToday": 0,
      "totalAmount": ********
    },
    "productPayments": {
      "totalPayments": 45,
      "paymentsThisMonth": 12,
      "paymentsThisWeek": 3,
      "paymentsToday": 1,
      "totalAmount": ********
    },
    "paymentStatus": {
      "successfulPayments": 65,
      "pendingPayments": 8,
      "failedPayments": 2,
      "cancelledPayments": 0
    },
    "revenueData": [
      {
        "date": "2024-01-01T00:00:00",
        "designRevenue": 500000,
        "productRevenue": 300000,
        "totalRevenue": 800000,
        "transactionCount": 5
      }
    ]
  }
}
```

## Cách test API

### 1. Sử dụng Postman/Thunder Client

#### Bước 1: Login để lấy JWT Token
```
POST /api/SystemUserAccounts/login-manager
Content-Type: application/json

{
  "userName": "SystemManager",
  "password": "@@abc123@@"
}
```

#### Bước 2: Sử dụng token trong header
```
Authorization: Bearer <your-jwt-token>
```

#### Bước 3: Test các endpoint
```
GET /api/Dashboard/overview
GET /api/Dashboard/user-statistics?period=month
GET /api/Dashboard/transaction-statistics?period=week
```

### 2. Sử dụng curl

```bash
# Login
curl -X POST "https://localhost:7000/api/SystemUserAccounts/login-manager" \
  -H "Content-Type: application/json" \
  -d '{"userName":"SystemManager","password":"@@abc123@@"}'

# Test Dashboard Overview
curl -X GET "https://localhost:7000/api/Dashboard/overview" \
  -H "Authorization: Bearer <your-token>"

# Test User Statistics
curl -X GET "https://localhost:7000/api/Dashboard/user-statistics?period=month" \
  -H "Authorization: Bearer <your-token>"

# Test Transaction Statistics  
curl -X GET "https://localhost:7000/api/Dashboard/transaction-statistics?period=week" \
  -H "Authorization: Bearer <your-token>"
```

## Lưu ý quan trọng

1. **Authorization**: Chỉ Manager và Staff mới có quyền truy cập các API này
2. **Period Parameter**: Hỗ trợ các giá trị: day, week, month, year
3. **Data Format**: Tất cả ngày tháng đều theo format ISO 8601
4. **Currency**: Doanh thu tính bằng VND (decimal)
5. **Error Handling**: API sẽ trả về error message nếu có lỗi xảy ra

## Các tính năng đã implement

✅ **A. Thống kê tổng quan:**
- Tổng số người dùng (Users)
- Tổng số đơn hàng (Orders) 
- Tổng doanh thu (Revenue)
- Tổng số thiết kế (Designs)
- Tổng số blog posts
- Tổng số reviews
- Đánh giá trung bình

✅ **B. Thống kê giao dịch:**
- Doanh thu theo ngày/tuần/tháng
- Số lượng giao dịch Design Payment
- Số lượng giao dịch Product Payment  
- Trạng thái thanh toán (Success/Pending/Failed)
- Biểu đồ doanh thu theo thời gian

✅ **C. Thống kê người dùng:**
- Người dùng mới theo thời gian
- Người dùng hoạt động
- Phân bố theo role (User/Staff/Manager/Vendor)
- Biểu đồ tăng trưởng người dùng

✅ **E. Hoạt động gần đây:**
- Người dùng đăng ký mới (top 10)
