﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dexin.Repository.Models;
using Dexin.Service.DTOs.User;

namespace Dexin.Service.DTOs.BlogView
{
    public class BlogDTO
    {
        public int BlogPostId { get; set; }

        public string? Title { get; set; }

        public string? Subtitle { get; set; }

        public string? ThumbnailUrl { get; set; }

        public string? Content { get; set; }

        public int? Likes { get; set; }

        public int? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public int? UserAccountId { get; set; }

        public int? Views { get; set; }

        public string? AspectRatio { get; set; }

        public List<string> BlogImages { get; set; } = new List<string>();

        public List<int?> BlogPostTags { get; set; } = new List<int?>();

        public UserProfileDTO? UserAccount { get; set; }
    }
}
