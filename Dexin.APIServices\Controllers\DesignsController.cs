﻿using Dexin.Repository.Enums;
using Dexin.Service.APIResponse;
using Dexin.Service.DTOs.DesignView;
using Dexin.Service.Interface;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Dexin.APIServices.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DesignsController : ControllerBase
    {
        private readonly IDesignService _designService;
        private readonly IImageService _imageService;
        private readonly IClaimsService _claimsService;

        public DesignsController(IDesignService designService, IImageService imageService, IClaimsService claimsService)
        {
            _designService = designService;
            _imageService = imageService;
            _claimsService = claimsService;
        }

        [HttpPost("create")]
        [Authorize(Roles = "Staff")]
        public async Task<IActionResult> CreateAsync([FromForm] CreateDesignWithImageDTO designDTO)
        {
            var thumbUrl = await _imageService.UploadImageAsync(designDTO.ThumbnailUrl);
            if (thumbUrl != null)
            {
                var dto = new CreateDesignDTO
                {
                    ThumbnailUrl = thumbUrl,
                    CanvasPosX = designDTO.CanvasPosX,
                    CanvasPosY = designDTO.CanvasPosY,
                    CanvasScale = designDTO.CanvasScale,
                    CustomerId = designDTO.CreatedBy,
                    Note = designDTO.Note,
                    PathUrl = designDTO.PathUrl,
                    Status = designDTO.Status,
                    Title = designDTO.Title,
                    Type = designDTO.Type,
                };
                var response = await _designService.CreateAsync(dto);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
            else
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "Đã xảy ra lỗi khi tải ảnh! Vui lòng thử lại!"
                });
            }
        }

        [HttpPost("create-3d")]
        [Authorize(Roles = ("Staff"))]
        public async Task<IActionResult> Create3DAsync([FromForm] Create3DWithFile designDTO)
        {
            var thumbUrl = await _imageService.UploadImageAsync(designDTO.ThumbnailUrl);
            var file = await _imageService.UploadLargeFileAsync(designDTO.PathUrl);
            if (thumbUrl != null)
            {
                var dto = new Create3DDTO
                {
                    ThumbnailUrl = thumbUrl,
                    CreatedBy = designDTO.CustomerId,
                    Note = designDTO.Note,
                    PathUrl = file,
                    Price = designDTO.Price,
                   // StaffId = designDTO.StaffId,
                    Title = designDTO.Title,
                  
                };
                var response = await _designService.Create3DDesign(dto);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
            else
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "Đã xảy ra lỗi khi tải ảnh! Vui lòng thử lại!"
                });
            }
        }

        [HttpDelete("delete/{id}")]
        public async Task<IActionResult> DeleteAsync(int id)
        {
            var response = await _designService.DeleteAsync(id);
            if (response.Success)
                return Ok(response);
            return BadRequest(response);
        }

        [HttpGet("getAll")]
        public async Task<IActionResult> GetAllAsync()
        {
            var response = await _designService.GetAllAsync();
            if (response.Success && response.Data != null)
                return Ok(response);
            return NotFound(response);
        }

        [HttpGet("getById/{id}")]
        public async Task<IActionResult> GetByIdAsync(int id)
        {
            var response = await _designService.GetByIdAsync(id);
            if (response.Success && response.Data != null)
                return Ok(response);
            return NotFound(response);
        }

        [HttpPut("update/{id}")]
        public async Task<IActionResult> UpdateAsync(int id, UpdateDesignDTO updateDesignDTO)
        {
            var response = await _designService.UpdateAsync(id, updateDesignDTO);
            if (response.Success)
                return Ok(response);
            return BadRequest(response);
        }

        [HttpPut("cancel/{id}")]
        [Authorize]
        public async Task<IActionResult> CancelAsync(int id)
        {
            var response = await _designService.Update3DStatus(id, ThreeDStatus.Cancelled);
            if (response.Success)
                return Ok(response);
            return BadRequest(response);
        }

        [HttpPut("update-3d-to-customer/{id}")]
        [Authorize]
        public async Task<IActionResult> Update3DAsync(int id, IFormFile updateDesignDTO)
        {
            var pathUrl = await _imageService.UploadImageAsync(updateDesignDTO);
            var response = await _designService.Udpate3DToCustomer(id, pathUrl);
            if (response.Success)
                return Ok(response);
            return BadRequest(response);
        }


        [HttpGet("getByStaffAndUser/{staffId}/{userId}")]
        [Authorize]
        public async Task<IActionResult> GetAllByStaffAndUserAsync(int staffId, int userId)
        {
            var response = await _designService.GetAllByStaffAndUserAsync(staffId, userId);
            if (response.Success)
                return Ok(response);
            return NotFound(response);
        }

        [HttpGet("getByStaff/{staffId}")]
        [Authorize]
        public async Task<IActionResult> GetAllByStaffIdAsync(int staffId)
        {
            var response = await _designService.GetAllByStaffIdAsync(staffId);
            if (response.Success)
                return Ok(response);
            return NotFound(response);
        }

        [HttpGet("getByUser/{userId}")]
        [Authorize]
        public async Task<IActionResult> GetAllByUserIdAsync(int userId)
        {
            var response = await _designService.GetAllByUserIdAsync(userId);
            if (response.Success)
                return Ok(response);
            return NotFound(response);
        }
    }
}
